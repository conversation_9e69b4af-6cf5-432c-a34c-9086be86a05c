# 01. 从对应到计数

好，欢迎大家来到胡老师的课堂，这是我们系列课的第一讲，我们的主题叫做从对应到计数。我先把标题写在屏幕上，从对应。到技术。这一讲我们的第一部分关心对应和多少的关系，对应于多少。 我想给大家说，技术其实是人类最最基本的需求之一。比如说开学第一天老师要给你发课本，那老师要拿多少课本呢？他就要数计数，提前数一数。爸爸妈妈要给你或者你的兄弟、朋友一起买棒棒糖，买多少呢？爸爸妈妈也要技术，一根、两根、三根。家里如果一共三个小朋友，一人一根，那他就需要给你买三根。 其实因为有技术这样子的一个需求，因为这个需求的存在，人们才逐渐有了一些概念，比如说数字。树。以及数位等等。这些概念我们会在这一章中都慢慢的学到。 当然在学习这些内容之前，我要告诉大家一个更加重要的道理，这个道理是数学学习的道理。每个年级每一节课我们都会学到不同的数学知识。但是在数学知识的背后有一个更为重要的东西，它叫做数学思想。 数学思想。如果我们去打一个比方的话，数学思想就像是数学知识的爸爸妈妈。没有数学思想就不会有数学的知识，不会有数学的概念。 而相比数学知识而言，数学思想才更应该是我们学习的重点。 什么是数学知识？比如我怎么计数，就是数学知识。那什么是数学思想呢？我们当然会学到的从对应到技术对应就是一种数学思想。 没有数学思想就不会有这些数学知识。这个观念我希望我们在听第一讲的时候就牢固地树立起来。现在我们会发现很多很多的老师，很多很多的课堂，甚至可能你的爸爸妈妈教你的时候，更多的也仅仅关注于知识。我希望你能够知道，学数学最重要的不是知识，而是背后的思想。 那什么叫做对应的思想呢？我们来想一个是想一个情景，你想象在很久很久以前，我们还没有数字，人类没有数字，也没有数，但是他却需要技术。 比如说我今天出去打猎了，我怎么告诉同伴我打到多少只兔子呢？我连1234都没有，或者有一个牧羊人，大家知道什么叫牧羊人吗？ 就是他每天会赶着羊出去，又会带着羊回来。那他早上出去带着羊出去放牧，晚上回来的时候，他怎么知道羊有没有少掉呢？ 我们小朋友说很简单，出门的时候我数一数他带了几只羊出去，那回来的时候再数一数。可是问题是现在他没有数字，他没有办法数1234，那他能怎么办呢？ 不同地方的人想到了不同的办法，我给大家介绍一种很经典的办法，他说这个牧羊人早上出去的时候，他自己有一个口袋。 可能他衣服袋里有一个口袋，那每出去一头羊，他带出去一头羊，他就往口袋里放一个小石头，再出去一头羊，我画一个箭头表示出去一头羊，他再往口袋里放一个小石头，再出去一颗羊，再放一个小石头。 他并没有123的概念，但是他总是知道，我出去一头羊我就放一个石头，于是出去了这么多，他口袋里就放了那么多石头，他带着羊在外面，羊吃完草到下午的时候，他就带着羊回来。 下午回家的时候，他做了一件相反的事情。什么相反的事情呢？ 他每回来一头羊，他口袋里是不是还有这么多石头，我来拷贝一下，把它放在下面。他说我每回来一头羊，我就擦掉一个石头，再回来一头羊，大家看啊，我再擦掉一个石头，再回来一头羊我再擦掉一个石头，这么不停的擦呀擦呀擦，当然我是在擦它是什么？扔掉一颗石头，我是在土上擦。那么大家想一想，等这些羊回来以后，他口袋里的石头会有几种可能？ 第一种可能或者我叫做情况一。杨东回来了。羊都回家了。但是，口袋里还有石头。 好，羊都回来了，口袋里还有石头，说明什么？说明一定有羊走丢了，因为我出去的时候，羊和石头是一样多的。 出去一头羊有一个石头，出去一头羊放一颗石头。我回来的时候，进来一头羊扔掉一颗石头，进来一头羊扔掉一颗石头，羊群进来我口袋里如果还有石头，那说明这颗石头对应的那只羊不见了，回来的时候羊比石头少。 这种情况就表示回来的时候羊比石头少，出去的时候羊和石头一样多，回来的时候羊比石头少，那是不是说明其实就是有羊走丢了。 那么情况二是什么？情况二就是羊都回家了，回来一只羊扔掉一颗石头，最后我们会发现，石头也扔光了。羊都回家了，石头也正好扔光，这个说明什么？ 出去的时候洋和石头一样多，回来的时候洋和石头还是一样多。那石头没有变过，所以出去了洋河回来的羊是一样多的那这个时候就是羊没有走丢。 杨梅走丢出去一头回来一头出去的回来一样多。那还有没有第三种情况呢？其实还有第三种情况是什么呢？我口袋里的石头已经扔光了，但是竟然还有羊要进来。石头扔光了，还有人要进来，大家想想这是什么情况？ 还有人要进来。 出去的时候羊和石头一样多，回来的时候石头扔光了，还有羊要进来。那说明回来的时候我的石头比羊要少，那也就回来的羊更多了，比出去的羊还要多。那回来的羊比出去的羊还要多，一定意味着什么？小朋友们，意味着我们把别人家的羊带了回来。 把别人的羊带回来。 三种情况。我们回到这个问题的开始。这个问题是说一个人连数字都没有，连数也没有，他怎么来知道出去的羊和回来的羊一样多。他想到了一种办法，用石头和羊去建立一种关系，这个就叫对应。 一颗石头对一个羊，出去的时候这样，回来的时候也这样，然后我们就能判断到底阳有没有变少。这种想法就叫做对应。我们认为对应是数字发展过程里的最早的一个阶段。数字发展的阶段。 第一个。就是用对应来进行技术。 有对应思想进行技术。 其实我们也可以不用石头，大家可能听说过一个词叫做结绳技术。我可以每出去一头羊找一个绳子，这样一个绳子在绳子上打个结，再出去一头再打个结，再出去一头再打个结。那回来的时候，我就解开一个结解开一个结，解开一个结，这也是一样的道理。 那还有人选择什么办法呢？找一个树，找一片树皮，我可以再出去一头羊，我就在树皮上划一道横线，再出去一头划一条划一道横线。 那回来的时候，划掉一条横线。请你停下来仔细思考一下，你有没有发现，不管我用石头，还是我用绳结，还是我用这个小刀在树皮上去划记号，其实背后都是一样的道理。 我没有教你三种方法，千万不要觉得这是三种不同的方法，其实它只有一种思想，这种思想叫做对应思想。我也想留一个思考题给大家，有了这样的思想，如果你是那个牧羊人，你能想到什么其他的办法吗？ 在没有数值的时候能不能进行计数？能想到的话，去教你爸爸妈妈一个这样的办法。 好，第一个阶段叫做用对应思想进行计数。但其实我们会发现在这个过程里，我们会产生一个概念叫做一样多，我们会产生一个概念叫做比较少以及比较多三种情况。分别对应了羊比石头少了，回来的时候羊和石头一样多，羊比石头多的情况。 那么用这种想法，其实我们就可以来进行比较了。大家看啊这其实是一个幼儿园的题。哪怕我们打开我们的这个学校里的课本，最开始的时候也有这样的问题。 所以下面这个图里圆圈多还是三角多，你可以试试看。 如果你拿这个图给你爸爸妈妈看，问他谁多的话，那爸爸妈妈一定会告诉你是三角多。但是他如果你问他为什么的话，他可能有两种回答。第一种回答说有三个圆圈，四个三角，所以三角比圆圈多。这其实他已经在用数字做了4比3大。 但更好的一种回答是什么？我不用管圆圈和三角各有几个，因为我们刚才石头和羊的那种关系，我们可以干嘛连线，这个方法小朋友们应该很熟悉，比如我虚线连起来一个三角连一个圆圈，然后会发现这里多了一个三角，没有圆圈跟他连。所以我们说三角多。三角形多。这种想法就是在做对应用对应在比较大小。 我们再看一个例子，比如我们说有一个教室里有一些小朋友，有一些椅子，我怎么知道是小朋友多还是椅子多呢？你想象一下你走进一个教室，这个教室里有很多很多的小朋友，也有很多很多个椅子，椅子都空在那儿，我怎么知道小朋友多还是椅子多。 有一个很快的办法就是我给大家说，我们大家去坐椅子，每人只能坐一个椅子，大家迅速的坐下，自己找椅子，坐你想坐的椅子。如果别人坐了，那你就坐其他的椅子。 只要小朋友都完成了这件事情，我一眼就能看出结果，我们让每个小朋友坐一个椅子。每个小朋友做一个椅子，找一个椅子坐。 那么如果。我们分几种情况，每个小朋友都找椅子，以后有椅子空着。小朋友都有一每个小朋友都做了一个椅子，但是还有椅子空着，那就是椅子多。那就是一子多。如果说第二种情况，我们说所有的椅子都坐满了，但是有小朋友找不到椅子坐。有小朋友没椅子坐。 说明小朋友多。 如果说正好每人一个椅子。正好每个小朋友一个椅子。椅子也坐满了，小朋友也没有站着的那就说明一样多，小朋友和椅子是一样多的。这个思路和刚才还是一样的，这是数字发展的第二个阶段。我们前面说数字发展的阶段，一是用对应思想进行计数。数字发展的第二个阶段，其实就是用对应思想来比较大小。 听到这里，大家应该已经越来越感受到对应思想的奇妙之处。 其实哪怕在这个中学，未来你们读初中高中的时候，最最重要的一个概念它叫做函数，我们现在小朋友应该没有听到过这个词，但如果你问问你的爸爸妈妈，这个东西当年一定把他们折磨的很惨。 很多人在中学学考函数，其实就是他从小没有好好的掌握对应思想，所以从某种程度上来说，我觉得在这一讲中，我们学对应思想比学怎么计数都更加重要。 好，再看一个例子，下面这个图里面还有好多好多个猫，有好多好多个狗，狗狗多还是猫猫多呢？现在要把它数出来好像很乱。 连线可以，其实我还可以做的更快，我都不用连了，我就一个一个擦掉。只要每次我擦一个猫猫的同时也会擦一个狗狗，那我就能保证猫猫和狗狗被插的一样多。 比如我们可以这样，我们把这个毛毛涂掉，把这个狗狗也涂掉，再涂掉一个猫猫，涂掉一个狗狗，涂掉一个猫猫涂掉一个狗狗，涂掉一个猫猫狗狗，我涂掉一个猫猫就会涂掉一个狗狗，我涂掉的猫猫和狗狗一样多。这样去掉了那么多以后，还剩一个狗狗，那就说明什么？ 在原来这个图里是狗狗更多。还是在用对应思想。 第一部分我们就讲到这里，在第一部分我们学习了对应有多少，接下去我们进入第二部分。第二部分，我们叫做对应与计数。 讲到这里，数字该出场了。那数字是怎么命名的？我们现在说1234怎么出来的？ 很多数学家都觉得数字的命名也是从对应思想来的。比如我们想象，我们最开始没有数字的时候，有人有一天想说我有两只狗或者我见到了两只狗，可是他没有1234。他怎么表示两只狗他可以这样，他可以指着自己的眼睛，因为每个人都有两只眼睛，他会说像我眼睛一样多的狗。 像我眼睛。一样多的狗。这个其实就表示两只狗，但当时他没有，渐渐的他都不用指了，他直接说眼睛，人家就知道是二的意思。所以世界上有一些地方的人，他是把二就叫做眼睛的。在他们的语言里，眼睛和二是同一种发音。类似的还在有些语言里，耳朵和2也是同一种发音。 这就是数字命名的数字发展的第三个阶段，我们叫做数字发展的阶段。三它是什么呢？它是数字的命名，但其实很多很多数学家认为数字的命名也是来源于对应思想。 今天我们不是重点来给大家讲数字发展的阶段，所以这个事儿我们就是顺便提供。你看技术大小比较命名背后都有这么奇妙的一个思想。我们从小渗透这个思想，我把它叫做小初高一体化。 就是小学、初中、高中有很多东西我们是要一以贯之的去渗透，这也是这个课程的一个核心的理念，不要把它割裂开。 现在我们回到让我们回到现代回到现代看看我们使用的数和数字。大家都知道我们使用的数叫做什么数字，叫做阿拉伯数字。阿拉伯数字不是阿拉伯人发明的，它是古印度人发明的，但是它是经过阿拉伯人传播的，阿拉伯数字只有十个0到9。 阿拉伯数字它是印度人发明的，但是由阿拉伯人传到了其他的地区，所以其他人以为当时人家以为是阿拉伯人发明的阿拉伯数字只有十个，01234567789，注意他只有十个。但是数有多少个？ 1 2 3 4 5 6 7 8 9 10 11、12，我们会发现数是数也数不完的。这个事情就很神奇了，数是数不完的。 可以一直数下去。所以我们经常说以后我们会说数有无限多个，或者说有无穷个，它非常的多。 但是这么多个数，我们竟然都可以用这十个阿拉伯数字来表示，这个事情就很神奇了。 所以数和数字之间是什么关系，我们打一个比方，数和数字其实不一样，数就相当于是我们学英语里的单词。我们知道英语字母一共就26个，但是它可以组成许许多多的单词。 那数字只有十个，但是它能组成很多很多的数。或者像我们的汉字和笔画，笔画就那么多，但是我们能写出那么多的汉字，数由数字组成，单词由字母组成，汉字由笔画组成。 那么根据一个数它所含有的根据一个数含有的这个数字的个数，我们也对这个数做了一个分类，我们把它分成一位数。两位数、三位数、四位数等等，这个看我怎么指。比如我这么写123，这些都是一位数。 那两位数比如我把一跟二写在一起，两个数字放一块儿，其实就相当于它占了两个位置，这就叫两位数。比如12 14，或者我随便写94，这个数有些小朋友可能还不认识。没关系，我们先把它写出来，你看到两个数字在一起，我们就叫做两位数。 比如说像这个叫几位数？你说它数字只有两个，一个一和一个零，但是零出现了两次，也就是零占了两个位置。这里是不是一共有三个位置？100那这种数我们就叫它为三位数。其实从这个位这个字我们可以看到，最最关键的不是他用了几个数字，而是他占了几个位置。很快的感受一下呗。 比如我来问问大家三是几位数？23是几位数？那当然毫无疑问三是一位数。23是2位数。 好，这一部分的内容我们先告一个段落，我们下一讲再见。 

# 02. 对应与十进制

好，欢迎来到胡老师的课堂。我们这一讲的主题叫做对应与十进制。我先把标题写在屏幕上对应。 十进制。在上一讲中我们说到有一个很神奇的事情，就是我们竟然只需要0 1 2 3 4 5 6 7 8 9 10样十个数字就能表示数都数不完的数。它的背后是一种数的表示原则，我们通常就叫它十进制。 在这一讲中，我们的重点是来感受，十进制到底是怎么样去产生的，到底是怎么样的思想在推动着这样的一种表示。 我们还是回到古时候，假设古人我们假设有一个很幸福的古人，比如在古埃及，但是他们是有巧克力吃的。那么他想知道我们一共有多少个巧克力，他可能能做这样的事情，假设这是一张树皮。 这里有很多很多块巧克力，我就随手画几个，当然我画的不太好，大家见谅。他就每一个巧克力，他就在这里画一横，就这么去数，一个画一横。 我画到现在画了四个巧克力，可能他这句话四五个、六个七个八个、九个10个11 10，20 30 40 56、17 18 19把他画到这里，他数了19个巧克力。但是我们想一想，他比如说算好了说我这里有19个巧克力，他就记在这张书皮上。 那别人过来了问他有多少个巧克力呢？他拿出这张纸给人看啊，拿出这个树皮给人看，说你看有多少刀就有多少个巧克力。 但是别人再要从头开始数这个事儿是不是很辛苦。就像我把一堆巧克力散落的放在外面，散装的放在外面，你要知道多少很麻烦。 但是如果把它整理好，五个一盒，十个一盒，最简单的当然我们现在用的是十个一盒，但其实五个一盒也行。比如说它就十个一盒十个一盒，十个一盒放在那边，那你可能看一眼你就知道，一盒加两个，一盒多两个巧克力，那一盒是十个又多两个，再往后数2，所以就是12个巧克力。 那个时候人家也这样想，我有这么多划痕数起来很麻烦。那我也做一个叫做整理工作，或者说做一个简化的工作。怎么样简化呢？就是我把十道横线用一个新的符号来表示。比如说我们可以随便来找一个符号，比如说他说这样，我就用一个圆圈表示十个横线。 一个圆圈表示十道横线。那么刚才我们看一下，我们来数一数，这里是1 2 3 4 5 6 7 8 9 10。好，他说除到这里是十个，那我就可以把这个擦掉，把前面这个打过横线的地方全部擦掉，换成一个圆圈。 再来，这里123456789。九个，那划不了了，没有办法换成一个圆圈，那就保留着。但是至少这样子人家来看的话会看的很清楚。 这里有十个，然后再往后数91234567八九十万。现在是19个，那我们小朋友可能还没有接触到更大的数。如果你已经学过更大的数了，你可以想一想这种方法是不是会更加方便。 假设你有45个巧克力，那我就是四个圈圈加上五道横线，这是一个很漂亮的处理。一条一条的横线很麻烦，我就把十条横线用一个东西去代替它。这个事情我们以后叫做代换。 它不仅在技术里面可以用，在很多时候都能用一个圆圈表示十道横线。 在这样的表示下，我们来看一看，我们如果有16个巧克力，我可以怎么表示？用一个圆圈还是表示十个巧克力？一道横线表示一个巧克力，我可以怎样表示16个巧克力呢？第一种办法我们说可以这样方法一。 我也不用圆圈，反正16个我就这么来。1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16表示完了。看着挺麻烦的，或者有同学可能说我写的好一点，或者我不这么写，我把它这样子去写。 1 2 3 4 5 6 7 8 9 10，像我们所说一样，数到十是一个节点，123456也可以。但是别忘了我现在用一个圆圈表示十个巧克力，所以我可以做得更快。方法2，我可以这样写。 一个圆圈就是十个，123456。好，圆圈下面对应着一个数字一，这个下面对应着一个数字6，但是这个一表示什么？这个一表示一个圆圈。 而这个六表示的是六道横线，它不是一个东西，一个圆圈，可是抵得上十道横线的。同样的一个一，当它代表一个圆圈的时候和它代表一个横线的时候，它是两回事。这个事情我们以后在三年级的时候会详细的展开，它叫做未知原理。 好，现在我们再来感受一下前面这个，大家先感受一下怎么样写比较方便。现在我们来做一个反过来的事儿，用一个圆圈还是表示十个巧克力，一道横线表示一个巧克力。那么两个圆圈一道横线表示我们有多少个巧克力呢？ 两个圆圈一个圆圈，两个圆圈或者我竖着写。一道横线像刚才一样，就像刚才这个感受一下。好，两个圆圈这里对应着一个数字2，一道横线对应着一个数字一，这个二代表什么？ 两个圆圈。这个一代表什么？一道横线。那一个圆圈是十个巧克力，所以两个圆圈是什么？ 先数十个再数十个，1 2 3 4 5 6 7 8 9 10，再1 2 3 4 5 6 7 8 9 10或者1 2 3 4 5 6 7 8 9 10。11 12 13 14 15 16、17 18 19 20，所以这里其实它对应着是有。20个巧克力。这个二对应着20个巧克力，这个一道横线对应一个巧克力。 如果从计算的角度来讲，我们会说其实它最后是一个加法，最后有多少个巧克力呢？就是20加1，这是从计算的角度来看的。但是我也可以不用计算的角度来看，即使我们现在没有学过计算，我同样可以来做这件事情。 反正就是这里二代表20，这里一代表一。我们把二和一放到一起组成一个新的数，把这两个东西合到一块儿组成一个新的数，这个数我们就叫它21。为什么叫21？ 因为二代表两个十，所以这叫二十一再来。 仔细感受这个图，现在我们现在来看一眼这个图，这里是2，这里是一。然后我们还是用一个圆圈表示十个巧克力，一道横线表示一个巧克力，两个圆圈表示我们有多少个巧克力，两个圆圈。横线现在一条都没了，没有横线就是两个人群。好，那这里是两个圆圈，像刚才一样写好吗？这里写个22代表两个圆圈。 两个圆圈，其实我们刚才已经知道，我们叫做20个巧克力。可是注意，如果我只从数字的角度来看，我会发现在这里。他这样单单表示一个二，如果我把这个题变一个，我现在不是问两个圆圈表示有多少个巧克力，我现在问两道横线表示有多少个巧克力。 两道横线那其实你这里下面也会写个2，但这个时候的2，你会写的它其实是两道横线。两道横线，它是表示两个巧克力。 我们说这样好像不大好，我不能既让一个二表示两个巧克力，又让一个二表示20个巧克力。因为总有一天我会脱离圆圈，脱离横线，我一直有圆圈，一直有横线，我毕竟写起来还是麻烦的。 我虽然在带大家感受这个过程，当然最终我们要只留下数字。 那当时我们只留下一个二的时候，我又怎么区分到底是它表示两个横线，还是表示两个圆圈呢？或者更精确的来讲，我到底是表示两个一开始表示两个十？那怎么办？我们就要引入一个东西叫做位置，不同位置的表示不同的东西。 现在是两个圆圈，刚才反而我不知道大家有没有这种感受，就我们来看这个图。这个图其实好像还不如这个图好理解，有横线比没横线好像还好理解一点。至少这个二和一在一起我是没有歧义的那这个我会产生一些歧义怎么办？ 我们说我就假想这里还是有横线的，但是有多少个横线呢？一根都没有，那我就是用零来表示。把2跟0放在这个东西叫做20，右边那个东西叫做2。 这两个东西我说它明显就不一样了。这个二是从右边往左边数第二位，这个二第一位，所以这个二代表两个一，这个零代表零个一，这个二代表两个十。现在我们已经脱离巧克力了，纯粹看数字了。 了不起。好，零的作用是非常大的。如果没有这个零只有2，我们会默认它表示两个一。但因为有了这一个零，我们就会说这个二是代表两个十的那我们数数知道12345678，一直数到22和20，当然是两个不一样的东西。 我们说你可以用横线，十个横线用一个圈。你在现在现实生活中巧克力可能十个巧克力放进一个盒子，那十个巧克力放进一个盒子，十道横线变成一个圆圈，这种就是我们常常说的叫做敬畏进一位，从一的这个位进到10的这个位，但是因为我们是满十个进一位。 十个巧克力就装一块了，十个横线就变成一个圈圈了。所以满十个进一位，这个我们就称之为十进制。 对应这个横线的位置我们回到这里，对应这个横线的位置的这个一，我们就叫它是个位。而对应圆圈的这个位置的它是代表几个十，你代表几个十的位置，我们就叫它十位。 我们再换一种场景，再帮大家来感受一下。我们这次换成小杆子，和我们的这个学校的教科书一样，换成一个小杆子。 我们看我们要数杆子，如果杆子全部散落着，我们也觉得很麻烦，那我就把十个杆子放在一起扎一捆，十个杆子扎一捆，所以这个就是一捆，多一根就是一根。 这是一款。这个是七根。 这个是一捆。这个是两捆，但是它多出来零零碎碎的一根都没有了，那也就相当于是零根。 所以你看它在这里个位是零，十位是二。这个个位是70位是一个位是一，十位是一。 因为我们觉得每次都画这个十植物的图形，画这个杆子比较麻烦，所以这个图是帮助大家理解的。还有这个小珠子来帮忙，个位上一个小珠子代表一个一，十位上一个小珠子代表一个十，所以这是一个11个1，一个17个，一两个10 0个一，这样我们就能来表示这个数了。 其实我们看啊从上到下是一层一层逐步的来抽象，我们最熟悉的当然是看杆子。然后现在说我把这个杆子扎成捆以后，把这一捆对应到一个珠子，这一根对应到一个珠子。 但这个珠子在不同的位置上，然后我们再把它放到我们一个叫十位，一个叫做个位上。你感受一下这个事情，我这里两个箭头就是一步一步的在变得更加抽象，从一根一根的杆子变成了一个珠子，从珠子变成了数字。 同样的他他到他。注意这里有一个零，这里有一个零，那零的话我们是不能去写的，零的话我们是不能去漏掉的。 好，那我们再来感受一下，现在我们用相对抽象的来感受。而这些图形表示的数是多少？ 我们看这里有几捆、一捆、两捆、三捆。所以我们说对应着这里三个珠子，这三个珠子一个珠子代表一捆，也就或者换句话说，一个珠子代表十根。 十位上写个三三就代表三个，十个位上写个一它就代表一个一。那么三个10和1个一合在一起，我们往后面数1 2 3 4 5 6 7 8 9 10，再数10个11 12 13，一直数到20，再数10个21、22、23，一直数到30，再数一个正好是31。 当然我们把三合一放在一起，我们也是这样读它的31。 这个是四捆零个0，跟单独的，我们读成40，这个四代表四个十，这个零代表零个一。这个零是非常重要的，你千万不能把这个零给漏掉。因为如果把零给漏掉，你只写一个四，那我们就代表四根了，它没有体现出是四捆的概念。 好，接下去我们要开始继续把它变蓝。 我想问问大家，我们前面最开始数数的时候，我们说一个巧克力画一横，一个巧克力画一横，1 2 3 4 5 6 7 8 9 10。 好，有十个了，他把这个去掉改成一个圆圈，然后再有十个加一个圆圈。 但是当我圆圈越来越多的时候，用这种用刚才的这种表示方法，至少能保证我什么呢？ 它至少能保证我所看到的横线不会超过10。因为你十个横线就变一个圆圈了，所以它再也不会出现这么看着很乱，一眼看不出有多少个数不清楚的情况，因为你会把十个变一个圈十个变一个圈，十个变一个圈。但是大家有没有发现，这样子下去，虽然你剩的横线会比较少，但是有可能你会有非常多的圈圈。 十个一圈十个一个十个巧克力打一个圈，十个巧克力打一个圆圈。那最后是不是会出现一种这样的情况？如果我有很多很多个巧克力，是不是有可能我最后会发现十个最后会画出这样的情况。 这种情况一眼看上去也挺讨厌的，它很像我们最开始说我为什么要用圆圈？因为我怕这个横线太多。那如果我圆圈用很多怎么办？注意同样的想法，我们可以继续往下去使用，什么意思呢？ 如果我要避免圆圈很多，我们之前是只有横线，第一步的时候我们只有横线。这是最开始的时候，然后我们进化到了叫做横线加圆圈。一个横线是一个圆圈，其实是十个横线，也就是十。十个横线。把它变成一个圆圈。 这个事情其实你可以一路做下去。比如你圆圈很多了，我们可以再引入一个东西，比如说叫做方块横线加。圆圈加方块可不可以？用三个东西来表示。十个横线是一个圆圈，那我们继续，我们还可以干嘛呢？十个圆圈再让它变成一个方块。 这个地方稍微有一点难，我们前面已经知道这样的表示其实相当于11根横线。那现在如果我出现一个这样的表示是什么意思呢？一个方块、两个圆圈，一道横线，其实它就相当于是。 我们知道一个方块是表示十个圆圈的，在没有方块的时候，它其实就是什么东西？他其实就是12个圆圈。因为一个方块是十个，就是1 2 3 4 5 6 7 8 9 10 10以上再加一个横线。 用这样的思路，在我方块很多的时候，我可以再拿十个方块变成一个新的图形。 很多同学说老师这个书好难，我还没学过这个好像都要超过100了，超过三位数了。如果你现在没有学到三位数，完全不要紧。因为我想给你传递的是这里面的思路，你怎么把十个横线变成一个圆圈，来让你横线可以少画一点。以后当你圆圈很多的时候，你也可以把这些圆圈变成方块，让你圆圈少画一点。 方块很多的时候还可以让方块再变成一个新的东西。比如我用一个方块表示100个巧克力，这是我的一种表示方法。我可以说方块表示一个巧克力，一个圆圈表示十个巧克力，一道横线表示一个巧克力。 但是如果你没学过一个方块表示100个巧克力是什么意思？假设你真没学过100，你也能把这句话翻译成什么呢？这句话可以翻译成用一个方块表示。 十个圆圈。 好，那现在他问你，一个方块，两个圆圈，三道横线表示什么？如果没有方块，我们知道我们能这样表示，这个代表一个三，这个对应一个二。 如果没有方块，假设现在没有方块，那它就是23个巧克力。那么现在如果有方块，其实我们要做的事情一样的一个方块，那我这里对应着一个一，前面我们说这个叫做个位，这个叫做十位，十个一就叫做十。 那么十个十再往上走一步，我们讲个10百千万，可以一路下去，其实我们可以用一个新的词就叫做百位。它对应的这个东西123放在一起，我们就会读成123。 对我来说，对胡老师来说，你们能不能去把这个东西读出来不重要。在一年级的第二讲的时候，我并不需要你理解100以上的数。在一年级的第二讲，我只希望你去体会从就上面这个东西，从横线到圆圈的逻辑和从圆圈到这个方块的逻辑是一模一样的，到这里就够了。 如果说你说你这个也能听懂，那你也可以试试下面这个例题。当然这个例题我会打一个星号，我们并不要求我们所有的同学在现在这个时候就能做，但对一些已经很不错的孩子，我们可以试试看这个图表示的数是多少。 你看现在我故意把个位、十位、百位都帮你写出来了，那个位是零，十位是300位是零，对不对？但是我们不会写030，最前面的零我们是不写的，最前面的数位不写0。 所以这个百位我们就不写它，如果把这个数写出来，我们就写30就可以了。那么下面这个各位，这里是三个珠子，我上面就没再画一捆了，因为100根画起来很难。这里是没有，我要写0，这里是4。 那我们可以读成403，怎么读也不重要，你只是感受这个四表示什么，这个零表示什么，这个三表示什么就够了。所以在这个时候，最前面数位不写0，但是如果某一位是空的，要用零来填补。某一位是空的，要用零填补。 好，这一讲的内容我们就讲到这里。如果我们把前两讲综合起来，其实我们一直在讲对应思想，从对应到计数，包括了对应和多少的关系，以及怎么用对应来比较大小，怎么用对应来计数。 到这一家是对应和10进制，其实整个我们现在这种技术的逻辑，这种表示的逻辑，一个圈代表什么，一个横线代表什么，它背后都在做对应。好，这一讲内容我们讲到这里，下一讲再见。 

# 03. 数的大小和比较

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是数的大小比较。我先把这个标题写在屏幕上，树大小比较。 生活中充满了比较，有的比较让人信服，有的比较让人焦虑和难过。比如说我们去比较我考的分高还是你考的分高，我的零花钱多还是你的零花钱多等等。如果没有比较，大概我们很多人的人生会幸福很多。 那那有些东西是可以比较的，比如说我的身高可以和你的身高比，我的体重可以和你的体重比。但有些东西是不能比较的，比如我的身高就不能和你的体重比，我的身高也不能和我的体重比。但是比较有意思的是任何两个数都是可以比较大小的。任何两个数。 都是可以比较大小的，树之间本身有一些顺序，这个在我们数数的时候就可以感受到12341个1个往后数，一个一个在变大。那么比较大小，我们在上一讲中学习了一些用对应的办法来做比较。这一讲中我们会看到更多的直接回归树本身来进行大小比较的办法。 比如我们先看一个很简单的例子，11和7哪个大谁大呢？我们可以有这样几种办法理解，第一种办法叫做数数。第一种理解。我们是这样说的，1 2 3 4 5 6 7 8 9 10 12点点点我就不写下去了。 数数的话，我们知道其实每往后数一个就是多了一个一没往后。数。一个就是多一，所以。7再加一个多放一个就变八了，再加一个变9，再加一个变十，再加一个变十一。十一在七的后面四格，所以我们会说11是比七要大的，而且大了一个四，这是一种理解。 我也可以用对应的方法像上一讲一样需要理解。用对应的方法，七就比如说这是七个圆圈，那十一就对应着比如11个3角。我们看圆圈多还是三角多，那其实这个去掉一个一个对着去掉，这里剩了这一点。 三角多其实也就意味着11要比七要。好，我们先把答案写上去，11比7 8。但是我们在上一节上一讲，我们还了解到了一个叫做十进制的东西。 所以我认为我们从十进制上来理解也是很不错的。 在这一讲中我们更多的要从这个角度去理解了什么叫十一。我们当时说划痕划一道横一道横一道横线就变成了一个圆圈。 或者巧克力一个、两个、三个、四个，每十个巧克力放成一盒。所以11我其实是看成什么一个十与一个一，一盒加一个。 如果按我们上一讲的这个理解，巧克力一盒十个，那就是一盒再多一个，一盒是十个。那七是什么？ 七就是七个一，那也就是七个它没有成和，所以你一个是凑不成一盒的，一个已经有满满的一盒再多一个了，所以十一也一定比七要大。这是我们的三种理解。 那么关于两个数的大小比较，我们有一个符号，我们叫做大于号。 什么叫大于号呢？ 就来因为我每次写中文会比较累，我们就会写这样一个符号，左边像一个嘴一样，左边张的大一点就表示它的左边是更大的那11比7大我们就可以写成十一大于7，那它把它反过来写，我们就称为小于号。 小于号就是这个嘴是对着右边的，嘴对着的地方就是大的那其实就是七小于十一，我们也能这么写。当然我们还有一个叫做等于号，这个大家应该都熟悉，就是两条横线两边张的一样大，一样大的时候我们就写一个等于号。 那么刚才我们11比7大，我们第三种说你把十一看成一和多一个七，就是七个凑不成和，所以11一定比七大。类似的如果我再问大家，比如说十一和八谁大？ 我们就会发现同样的道理，十一是一盒十个，我就是一盒再加一个八，一盒都凑不满，所以11会比八要大。那大家再来一个，比如15和95是1盒加五个九就是九个，一盒都凑不满，一盒始终是十个。 在十进制下。 好，于是我们似乎能得到一些结论，叫做A11比7大，11比8大，15比9大。我们会发现好像这里都是两位数和一位数在比，这个都是两位数，右边都是一位数。所以最基本的一个结论，我们把它写出来叫做两位数总是比一位数要大。 就用刚才的理解。用一盒一盒的理解，两位数上的每一个数字已经代表几个十了，十位数上的十这个数字代表几个十个位上的只代表几个一，所以你一位数永远只有不到10个1，人家已经至少有一个十了，所以总比你要大。 这样大家感受一下，我们是从最最特殊的情况，11比71和85和9，我们慢慢的做了一件事情。这个事情从很特殊的情况来看到更加一般性的情况，叫做从特殊到一般。 接着我们再来看，两位数和两位数怎么办呢？23和41哪个大呢？像刚才一样，我们说可以数数吗？可以，我数到23再往后数可以的。 但是我觉得从一开始数好像太麻烦了，用对应可以吗？也行，但是如果你是这里画23个巧克力，那边画41个巧克力，我也觉得比较麻烦。所以这两个现在好像都太麻烦了，我们不高兴这样做。但是刚才的十进制的意义是最简单的。 我们看23是什么？23如果还是一盒是十个，它是什么？它是两盒再加三个，两盒又三个，或者两盒多三个，有三个凑不成一盒，剩下的有20个凑成一盒了。那41是什么？ 41是4盒多一个。我们知道我们去比较的时候，我总是比较少。我们看到有散装的，有一盒的，我想要更多的巧克力，我肯定先要盒数多的那一个。这里放着四盒，那边放着三盒，那我肯定要四核的那一个，所以整合多的肯定更大。 这个整合就是什么？就是十进制。就是这个十位数，对不起，这个整合就是十位数，也就是这个二代表两个十，这个四代表四个十，这个三代表三个一，后面这个一代表一个一，我优先关注几个十的这一部分。 我们来总结一下，一位数总是比两位数小。前面我们已经得到这样一个结论，叫两位数比一位数大。反过来讲就是一位数比两位数小。 现在这个两位数和两位数比，这个题的答案我们会发现41更大，二十三更小。所以我们写这个大于小于号的时候，张开的那个部分向着41，写成23，小于41。那两位数和两位数比我们优先关注谁带的这个十更多，也就是十位数上更大，两位数与两位数比较。 刚才是两位数比一位数大，现在是两位数与两位数比大小。 十位大的更大。那十位一样大怎么办？比如说41和42怎么办？那就很简单，十位一样。大家都有同样多个十大家都同样多和，那就看剩下的零散的部分谁更大。也就是说十位一样的时候再看个位，十位相同时。个位大的更大。 好，但这有没有包含所有的情况？一位和两位，两位和2位。但是我们其实还有三位数，还有四位数。可能我们有些同学学到了，有些同学没学到，没关系。但我们可以想象同样的道理，两位数比一位数大，同样的道理一定会有三位数比两位数大。所以最最一般的情况是什么？我们要去比较两个数的大小。 我们先看第一件事情，谁的位数大？优先比较数位，先比较数位，数位大的数更大。 那么数位一样怎么办？大家都是两位数，大家都是三位数怎么办？都是两位数的时候，我们就先比较十位，再比较个位。那都是三位数，我就先比较百位，再比较十位，再比较个位，所以数位相同时。 这个结论的理解比这个结论本身更重要。大家一定要知道为什么我们这样数位相同的时候优先比较最高位，先比较最高位。再依次比较。如果最高位一样，我们再比较下一位。最高位相同时。 再比较下一位，这件事情可以一直做下去，下一位再一样。我们再比较下一位，我们一位数和两位数的道理和两位数、三位数的道理一样。 两位数和两位数的比法和三位数与三位数，四位数与四位数，它也是一样。就像我们上一讲中学习的，说我怎么从满10进10个1变成一个十，到后面有十个十，我再引入一个新的东西，都是一个道理。 好，我们现在来做一些具体的例子，用大于、小于和等于填空。十一和四谁大呢？十一是两位数，41位，数11比4要大。位数不一样，数位不一样，谁的数位多谁就大。那十一比四大4和11比那我也可以这么写，写成四小于十一。好，11和23比大家都是两位数。 先比较十位，这个十位是一，这个十位是二，所以1比2小，那11就比23要小。那么那边我就会写成23大于十一。好，11和15比大家的都是两位数。先比较数位，大家都是两位数。 十位都是一都是两位数的时候，先比较十位，十位一样，那么再比较个位，个位11 11 15 15，它比11比15不小，同样的15比11多，这是一个很简单的例子。那么这个例子，我故意每个都写了两组，让大家体会一下。 其实它有一个这种感觉叫做我比你大。我反过来写，就是你比我小，这其实是一种。 大于号和小于号的某种对称性，之后我们会再找时间，会在后面的讲次中我们再来讲到图形的时候，会来解释什么对称性。 接着我们再来看，下面八个数里面比53小的有几个？我们很快的先看一遍，两位数、一位数、两位两位两位两位两位三位。 好，先比较位数，两位数一定比三位数小，两位数一定比一位数大。53是个两位数，所以53肯定比五要大，也就是五要比五十三小五，肯定是一位数，肯定比两位数小。 那么一百肯定就不是，因为100是3位数，三位数肯定比两位数要大。 大家都是两位数的情况，我再比较它的，先比较十位，我们先写出五是小于53的。好，那么67和53比，它的十位是六，53的10位是五。所以67比53大，14和53比，14的10位是一，53的10位是五。 指甲大家都是两位数，我比较十位就好了。那同样的31，72就比五十三大，50比53小。50和53比的话，大家都两位数在十位一样，都是个位，0比3要小。那53和53这2个数是一样的，所以我们会写它相等。 我还漏了哪个？还有一个一百大于五十三好，那比53小的有几个？54、31、50，我们数一数，12三四个，12三四个，所以我们说有四个。是五十四、31和50。 好，最简单的问题我们看到这里用15084张数字卡片能组成的最大的两位数是多少？一比如说我来帮大家换一下卡片，五四张卡片。0。 好，还有一个八能组成的最大的两位数是多少？一共就四张卡片，那每张卡片只能用一次。而且他说了括号里说卡片不能倒置或者旋转，就不能把卡片翻过来。你不能说我把五看成二之类的，现在不带这么玩，就是这四个数字。 好，那最大的两位数，我们知道两位数和两位数比，怎么样的两位数最大呢？我要十位大它就大。因为我们优先比较十位，所以现在最大的这四个数字里最大的数是8。 那我要把8放到十位上，所以最大的。8放在十位上，那么个位上放谁呢？十位一样的情况，个位是越大这个数就越大。 所以个位我就放第二大的就是五。 那么最小的两位数是多少呢？我们说我就要十位最小，现在这四个数字最小的数是0，但是零不能放在十位。如果是个两位数的话，零不能放十位，三位数我们说反而可以了，最高位不能是0。 那么现在是所有的数据去掉0，最小的是一，所以最小的两位数肯定是一开头的。那么个位放几个位越小越好，各位能不能放零呢？10这个数是可以写的，10，所以最小的两位数就是10。 好，然后我们再来看这个问题，把它变难一点，现在问能组成还是用15084张数字卡片能组成？最大的三位数是多少？最小的三位数是多少？ 同样道理，我们说最大的三位数与三位数，那我先比较最高位，先比较我们的百位，那么百位放谁最大呢？1508，把最大的数放到这里去，也就是8。如果大家现在还没学到三位数，你在看这个视频的时候，那你就等学到三位数再回过头来看，或者你可以结合前两讲的内容，试着理解三位数。 好，最大的这个数字八放在这里，那么第二大的放在十位，第三大的放在这里。那最小的三位数呢？我就把越小的数字放在越前面，但是零不能放百位，所以百位先放在一，已经是我能找到最小的了。 那一用掉以后还有5和0和8，谁放到10位呢？百位相同，十位谁小谁就更小。那我就把最小的放过去就是零，15。 好，现在这个题还不算太难，因为它是告诉你大家都是三位数。如果我再要变难，直接就会问你这个大家可以留作一个思考，直接就会问你说最大的数是多少？能组的最大的数四张卡片随便你用几张，但能组成的最大数是多少？ 能组出的最小数是多少？如果我把这个三位数的条件去掉，大家可以做一个思考，这个就留住客户思考了，因为它难度会更高，它会牵扯到四位数。实际上这种问题被很多人称之为叫做组数的问题。 实际上组数的问题就是什么呢？就是大小比较。当你理解两个数的大小比较，先看大家是几位数，然后位数一样我们再去看这个先先看这个最高位，然后再一步一步看下去，理解这个事儿就行了，其实是一个思路。好，它本质上是大小比较的变形。 看这一讲的最后一个例子，在71、17、27 4个数中，不大于17的有几个？不大于是什么意思？我们说我两个数，我和你比，我要么比你大，要么我就是不比你大。 注意，我要么比你大，要么不比你大，但我不能说我要么比你大，要么比你小，因为它还有什么相等的情况，我们可以做这样两种分类。第一种叫我比你大，也就比十七大的是一类的那第二类叫做比不比十七大的。你每一个数要么是比十七大，要么就是不比十七大，这是一种分类。 还有一种分类叫做什么呢？那两个数据比大小一共有几种情况呢？ 比十七大与十七相等。以及比十七小，所以你看要么是比十七大，不比十七大这样区分，要么是比十七大之外，还有与17相等和比17小，所以不大于17就是这个不比17，大家就是不大于17，或者是这两个合起来也可以不比你大，就是和你相等或者比你小。 那我们四个数分别做比较，现在对我们来说很简单。第一种叫做，如果我们用第一种思路的话，我只要把比十七大的数出来，有几个剩下的都是不比十七大的，相当于做它一个逆向思维，做它反向的这件事情。 有点像我说我要把班里的男生都找出来，全班我要把男生找出来。其实我把全班女生叫他的站到教室外面，那教室里剩下的就是男生了。 好，那比十七大的有几个呢？我们第一种理解。先把比十七大的找出来，比十七大的。有只有27一个，只有一个，所以不大于17的1共有四个。那你去掉这一个以后，就是剩下的这个70 17就剩下三个，所以不大于17的我们就有。三个，这是第一种理解。 第二种理解，我可以把所有的情况都写出来。也就是找小于17和等于17与10七相等的，以及小于17的与十七相等的那只有一个。只有17一个。小于17的。 有七有十一是两个，所以我们也会说，你这里一个，这里两个放在一起，所以不大于17的。有三个，这个三其实就是1加2。好，我们再把四个数写出来感受一下。七小于十7，十一小于十七，十七等于十7，2十七大于十七。 现在我不大于17，所以第一种办法就是不大于十七，我把大于17的去掉了，那就剩下这些，就剩下一些。或者你可以理解成我把这个划掉，剩下的就是不大于17。 那么第二种就把小于17的和等于17的找到正的数，一个、两个、三个都行。好，这一讲的大小比较，数的大小比较，我们就学习到这里，我们下一讲再见。 

# 04. 加减法的奥秘

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是加减法的奥秘，我先把标题写上了加减法的奥秘。什么叫奥秘呢？ 我们同学说加减法我们都很熟悉，我都会。但是在这一讲中，我们想把这个非常简单的东西给他往下挖一挖，挖的深一点，来做一些比较有意思的思考。好，那么加和减毫无疑问是数学里面最最基础的两种运算加减。 那什么是加法？什么是减法？我们从一个最最简单的例子来看起。比如说我的左手有两只花，我的右手有三支花，我一共有几只花？我们知道简单来说加法就是合起来。就是和也就是增加。我们有时候用专业一点的话来说，加法就是把两个数或者几个数合并成同一个数。两个或者几个数合并成同一个数合并。 拿我这个花的例子来说。左手2只，右手三只，我一共几只，用加法写出来，我们知道叫做2加3等于5。但是2加3为什么等于五呢？ 我们现在回到没有加法的时候，我们来回答这个问题。如果我没学过加法我会怎么做？我可以把我左手的两枝花，比如我这个就代表一枝花，这是我左手的两支花，我右手有三支花，我可以把我们这些花呢都放到地上，这是我右手的三只画，然后我从头到尾数一数，我可以这样子数。 12345数出来一共五只。这么来看加法似乎就是一个数数。我不仅可以从头数，我还可以从什么某一个节点开始数，最左边我叫做从头数。 这是从头数的。 那什么叫从某一个节点开始数呢？就是我知道我左手一共是两枝花，那我左手我就不用数了，这里就是两只。我从右手接着数右手，那右手是一，不对，右手应该不是从一开始。 因为左手2只，右手是从三开始，所以右手这接着数就是345。你看左手数完两只，相当于已经数过了。我知道那边有三只，所以我从二开始接着数三个数，右手就是接着数三个数。 但是我们是从几开始数？是从二后面开始数。我们小朋友说你可以从头数，你可以从左往右数，对不对？ 这个叫做从左往右或者说接着左边的数。 那我能不能这样？我能不能接着右边的数？ 我们来感受一下，虽然这个题非常简单，但这里我们会发现有一些很好玩的事在里面。右边一共是三个，这是我知道的。 我从右三个之后再接着数左边的，那么左边还要往后数两个。现在不是从一开始是从三后面往后数两个数。 好，于是那边三个，那这就是第四个，这就是第五个，我也能数出来，一共是五个数，这个叫接着右边数。 我们小朋友说，胡老师你越搞越复杂，这不就2加3等于五吗？没错，2加3等于5。第一种看法全部放出来，12345数下来。第二种看法2加3，左边两个，右边三个，左边两个放好，然后再接着往后数3，或者右边三个放好，再接着往后数2。 哪种数法简单？如果我们从数数来做加法，我们来看是接着左边数简单还是接着右边数简单，在这个例子里我们会说这个更加简单一点，为什么？ 因为这个实际上你只向后数了两个，一大一小，一定是接着大的数，小的更方便。如果我把数字变大一点，你就会感觉更明显。 比如我左手不是两只花，如果我左手这个两枝花改成八只花，右手三指划，那我从头开始数是不是很麻烦。但是我把它八个三个全部都画出来再数，这就叫从头开始数，很麻烦。 但是如果我接着左手的去数，右手八之后数三个数字，9 10 11只要数三个，你接着右手数着，我左手拿你三之后要456788888，一直要数八个数。所以我们更愿意，如果用数数来做加法，我们更愿意接着大的数小的，这样更方便一点，就是我们常常说的站在巨人的肩膀上好。 那么为了让加法更加直观，我们还是来看3加2这个问题。有时候我们还会借助一个图形，用图形来理解加法也是个很好的事情，比如这里一开始这个地方叫做0。 一我们把一个个数标好，23451个格子就是往后数一，所以3加2我可以怎么表示呢？三在这里加个2，就是往后数两个跑到这儿。 它对应的这个数字就是五，那么2加3，或者这是我刚才接着三往后数两个，那我还可以接着二往后数三个。那二在这儿接着它往后数三个，123也是指向5。 往后数三个，我这里写个加3，表示往后数三格，所以红的那个有时候我们会愿意把它写成。3加2蓝的这个，我们有时候会愿意把它写成2加3，但其实它是一回事情。 好。 这种想法，我们把数和图放在一起以后，我们会一直学到这种想法，这种想法叫做塑形结合。把数和形放在一起，数就是数字，形就是图形。中国有一个很有名的数学家叫华罗庚，他写过一句话叫做树无形式。少直指。 什么意思呢？如果只有数字没有图形，不够直观，这个直觉就是直观的意思。他还写过一句话叫做形少数时难入微，这个词大家可能比较难理解，形少数时就是有图形，但是少了数字的时候很难干嘛呢？入微。就是从更细致、从更微观，从更小的这种场景来感受它。 所以很多时候我们希望把数和形放在一起，用数它表示比较精确，你有八个糖葫芦，我有六个糖葫芦，你肯定比我多两个。图形它又很直观，八个糖葫芦串成这么多，六个串糖葫芦串成这么多，各有各的好，但是也各有各的缺点。 以后我们会看到很多很多的例子，我们愿意把数和形放在一起。在小学阶段可能更多的是图形来帮助数字，到以后的阶段可能更多的数字来帮助我图形。 好，这个就是从数数的角度来理解我们的这个3加2。但其实加法我除了数数的角度，我还可以从什么呢？还可以从对应的角度来理解。什么叫对应的角度呢？ 我们上一讲中学过对应，我们还是拿那个三个花两个花的例子来说，我左手两个花，右手三个花。这里怎么被我擦掉了。把它补一下，就不知道什么时候擦掉，这里前面我写的是从头数。从头开始数。 好，我左手三个花右手两个花，左手两个花，右手三个花。我总共有几个话就是我们最开始的一个例子。这个时候当我说我总共五个花的含义是什么？它其实是这个意思。比如这是我。这是某个人叫小明。 我们说3加2等于5，或者说2加3等于5。 其实它意味着是当我两个花三个花的时候，另外一个人如果有五个花，从对应的角度来理解，我的花就和他的花能够形成一个一一对应。 就是我们最开始学的时候，整个系列课程的第一讲，我们讲到对应来比较多少2加3等于5，或者说3加2等于5，它背后的对应就是我现在这个图所展示出来的。 到这里为止，我们给出了加法的三种理解。我们可以从哪些角度来理解加法？我们写一个小小的总结。 加法我们有三个角度给出了理解，第一个角度就是数数。从头数接着某个数数等等。 那么数数我们也可以从图形的角度来理解，当然它其实是和数数类似，我还可以从对应的角度理解，对应可以比较多少，一一连线比较多少。我们在第一讲就学了，当我们说2加3等于5的时候，其实它就意味着两个和三个放一块儿的时候，和5个之间就能做出一个一一的对应。 大家全部都连起来就会一样多好。 其实我们还得到了非常好玩的事情，先数左手的花朵再数右手的话，左手两个，右手三个，有时候我们会愿意写成2加3。那先数右手的话，再数左手的话，有时候我们愿意把它写成右手三个，左手两个，我们就会把它写成3加2。 但你会发现你不管怎么样去写，它的结果肯定是不变的。就像爸爸有三个糖，妈妈有四个糖，他们的糖都给了你，爸爸先给妈妈再给，还是妈妈先给你，爸爸再给你。 总之你就是把他们所有的糖放在了一起。 我们把数字变掉不是2加3 3加2。比如说你把它变成4加5，他也一定等于5加4，这件事情也成立，你再去试试看8加3，不要说我还没学过这种加法，用我们的数数，用我们的图形，你一定是能做的。 8加3等于3加8，只要会数数就会加八了。那这个事情你去算，它也是成立的那这个事情很有名，这个事情叫做加法交换律。 什么叫加法交换律呢？交换就是我可以换，对不对？就是说我把一个加法两个数换一换位置，8加3换成3加8，它的和是不会变的那这件事情我们院一开始就给他做总结，叫加法交换律。 他说的是交换两个加数的位置，它们的和不变。那如果我用一个很简单的图形来帮大家写的话，就是一个圆圈加一个方块，它一定等于一个方块加一个圆圈。用专业一点的话，交换两个加数的位置和不变，关键不是用什么换，关键是理解它背后在做什么。 好，如果说我们理解这件事情以后，我们就可以再来看看减法五减2又是什么意思呢？当然我相信很多同学都马上能反映出来，5减2等于3。那5减2我可以最简单的一个理解就是我本来有五支花。 被拿走三只以后。被拿走两只以后，还剩几支花？ 这就是5减2的理解。如果我们拿一个图像来说，12345好，被拿走了两只，我就用一个绿色的把这个涂掉，这个没了，那还剩几只？我也可以数数还剩三只，但是这个数我不希望大家是从头数，我们有一个更简单更好的数法。 就是因为我们知道数数是这样12345，我们说加法是数数，加法是怎么数？是正着数。那么减法其实就是倒着数。 5减2等于几？就是五往后倒着数两个数字，那五往后的第一个数字是44，再往后一个数字是三，所以5减2就是倒着数两个字。那也可以用刚才这个图形来看。 我们刚才画过一个图，012345减2，什么意思呢？五一开始在这儿，加法我们是这样子过去往后走，那减法就是往前走。所以5减2就是这里对应着一个三，这也一样的。 怎么样理解加法？反过来就是一个减法的事情。那我能不能用对应？当然可以。5减2等于三什么意思？就是我有五枝花。我有五支花。这个是我我有五支花，比如有个小明，他有三支花。我的五支花被别人拿走了，不是小明不知道谁拿走了两只，那剩下我的花就和小明的花一一对应，那这就是5减2等于3。 再来看我还可以从加法的角度来理解减法。因为我们刚才说减法，你不管从数数、从数形结合，从这个对应，都和加法非常的像都和非加法非常的像。既然这样，那你有没有发现整个过程加法好像就是把减法去反过来了。 什么叫5减2等于3？最开始的时候人们可以说是没有减法的，我们去未来算，或者说有些同学现在可能已经是这么算，你想一想你怎么去算5减2等于几的？5减2等于几？实际上如果我们脱离开这些生活的实际场景，脱离开这些数数5减2等于几，其实你心里想的是什么？ 五个往回数两个会数到几，那其实就是谁正着数两个会数到55往往回数两个数到几，就是谁正着往后，谁往后数两个会数到谁，这两件事事情是一样的。五回数一个回数一个数到的这个数就叫5减，那其实也就是这个问号，我往前数两个数到5，那往前数两个数到5的那不就是对应一个加法的事情。 它其实就是说谁加上2。等于5。 刚才我们画这个图大家也可以感受一下，我们在画加法的图的时候是这样，你看这里谁三往前数两个数到了5，那么现在5减2 5，往后数两个退两个就退到3，那还是说明5减2找的那个数它加2。就是这两件事情是完全一回事。 从对应我们刚才也解释过了，所以在这里我们想给出的是怎么样一件事情，我们加法说有3种理解，同样的我们可以总结减法。首先我也有3种理解，我也可以从数数的角度理解。 我也可以从图形的角度，其实我可以把这一块完全抄过来，一模一样。我也可以从图形的角度，我也可以从对应的角度，我给大家先讲加再讲减去，做这样一个过程，做这样一个梳理。 看着一方面减法像是加法的一个复习，一方面是一个再认识，就是看的更深一点。但其实在这里如果我们加减法都看完了以后，我们说减法可以看成是加法反过来的一个运算。 什么是减法？减法就是加法。反过来以后我们会用一个更专业的词，反过来以后我们会把它叫做互逆。现在这个词我们可以先不管它。 好比如说我们再来感受一下，我们说我怎么理解5减2加2呢？5减2加2我们说5减2，那我们已经会了5减2就是三。所以第一种理解法一，我们说5减2加2 5减2就是33，再加2，那就是五。 但第二种理解是什么呢？我可以不用算，我会这样子去想，从生活里说减二再加2，就是先拿走两个再加两个，先拿走两个再补上两个。我本来有五支花，你先拿掉我两个，你就再还给我两个，那不等于我没有变多，也没有变少，所以答案不变。本来五个还是五个。原来五个还是五。 如果我纯粹从数字去理解它是怎么一回事情呢？就从数学来理解，5减2加2，这是这节课最难的一个东西。什么叫5减2呢？5减2等于几就是几加二等于5，这是我们前面的内容。所以5减2加2可以这样理解，5减2它表示的这个东西就是加二等于五的东西。 5减2是什么？它是一个。加上二等于五的数。 这就是5减2的1种理解，5减2这是一个数，这个数满足什么？这个数应该有什么条件？减法和加法的关系告诉我，你5减2等于多少，这个多少加上二就是五。 那现在我最后要算什么？我不就是要算这个加上二吗？你看最后算的是这个东西再加上2，而左边这个东西代表的意思就是加上二等于五的东西。 那我现在加上二当然等于5，所以5减2加2就等于5。 我跟他说，胡老师，你这搞了半天还是5，我没觉得简单，其实。你如果再仔细想想，我们在这个讲法里，你是一点都没做过计算的。 只是因为数字很小，你没觉得简单。现在是加减法，以后是乘除法，到高中我们甚至要学指数对数，它背后都是同样的道理。 实际上一旦你理解了加和减的这种关系，你未来也会理解乘和除。碰到这一类问题的时候，实际上这个法三它根本就没有做计算，我们算都没有算就能把它弄出来。 什么叫5减2 5减2就表示一个数，这个数加二等于5。那现在你问我他加二等于几，我就直接告诉你是5，我没有算5减2等于三这件事情。 好，这个问题比较的抽象，这一讲虽然我们只讲了二十多分钟，但是它的跨度是很长很长跨度很大。我们可能每一个同学会发现，这一讲没有一个例题我是不会的。因为胡老师好像一共只讲了2加3和5减2 2个例子。 但在这里其实我是想帮助大家理解到底什么叫做加法。从不同的角度理解加法，从不同的角度理解减法，最后再理解一个加法和减法的关系。好了，我们这一讲内容就讲到这里，我们下一讲再见。 

# 05. 凑十，平十和破十

好，欢迎来到胡老师的课堂。我们这一讲的主题是凑十、平十和破10。 我先把标题写下来，凑10平时和迫使，我们有一些同学可能都听说过凑十法、平十法、破10法。有一些同学可能第一次听说，没有关系，我们先看一看，至少大家都有一个十。 在这里可见这三种方法他们至少有一个共同点，或者说他们都有同样的一个想法，就是去找10。 那为什么要找十？这三个方法具体又是什么？它怎么用？它背后有什么相同的相通的地方。通过这一讲的学习，我们都能看得很深刻，看得很清楚。 在所有的加法里面，其实最简单的加法应该说是这个样子的。上一讲中我们说可以用数数来看，对吧？ 可以用这个图形来看，可以用对应来看。其实回到最开始计数的时候，我们就说为什么我这个十进制是怎么来的？ 我们可以想象成我要数什么东西，1234每一个东西我就画一条横线，但是横线变多了，十条横线的时候我就画一个圆圈。那么类似的10加1相当于我们在第一讲中一个圆圈一条横线，所以它就是十一，10加2就是十二，一个圆圈两条横线，10加3就是十三。 这是我们最最简单的东西。 如果再套用一个生活场景，我们可以把它想成这样，满十进一满十进一。我们家里如果买鸡蛋的话，我们假想有一个鸡蛋盒，这是一个鸡蛋的盒子。 它里面有十个空十个格子，鸡蛋放在格子里它就不会掉。那所谓我拿一个圆圈表示一个鸡蛋，十个鸡蛋，这就是一盒就是十个，有一个零碎的，十个一盒再加一个就叫做10加1等于十一，这个圆圈表示鸡蛋。 那如果是十加二是什么意思呢？10加2对应的就是这样的鸡蛋，就是一盒再加两个鸡蛋。 十二本身我就能理解成这个事儿，叫做一盒加两个。那么不管是10加10加20加3，一直到10加9，其实我就直接是一盒加几个。 它的好在哪呢？好在第一盒是满的，它整整装满了十个。 但是很多时候我们会碰到的问题没有那么舒服，我们可能会碰到类似于这样的问题，8加4等于几？那8加4等于几呢？ 我们现在不从数数来看了，我们回到这个运算来看，我还是可以从鸡蛋来理解，那8加4相当于什么含义呢？其实它是这样一个图，一盒最多放十个，那么8加4其实就是这是一盒，但这一盒没放满，它只放了八个，另外一盒他放了四个鸡蛋。 他问你，我把这个擦掉，四个鸡蛋，他问你总共有多少个鸡蛋，这个就是8加4。 那在这个时候，我们先脱离加法。我们就想在实际情况中，你如果看到你们家有一盒鸡蛋明明可以放十个的，它只放了八个。另外一盒鸡蛋明明可以放十个的，他只放了四个。 你会不会总觉得这两个鸡蛋好像没有一盒是圆满的，没有一盒是放的我特别舒服的。我们会有一种强迫症，希望自己想干嘛，希望自己想把这两个鸡蛋放到这里去。把它整理成这种样子。 整理成这样的样子，第一盒把它放满了。第二盒还剩两个，但这个时候第二个还剩两个，这个时候我就认为这个加法我已经做完了。 因为它其实看起来就已经不像是一个加法了，已经像是什么就是整理之代码。所以现在你八个鸡蛋加四个鸡蛋，一共有多少个鸡蛋呢？ 我从整理的角度来看，上面是8加4，下面就变成了10加2。 这个当然我知道是十二，那鸡蛋数量没变过，我只是重新放了一放，所以8加4也是十二。大家再体会一下这个过程，我把八个鸡蛋想全一盒最多装十个，因为我满十要进一盒，我不可能装超过十个。 但是第一盒没放满，只放了八个，第二盒也没放满，只放了四个。那一共有多少个鸡蛋呢？ 我把法理解成是一种整理鸡蛋的过程，你这里有四个，这里有八个，那第一盒没满，我就第二盒里挑一点移到第一盒，把第一盒放满。那这样第一盒就放满了，就是十个，第二盒还剩两个，所以它就变成10加2等于12。 那具体在做的时候，我们实际上是这样子。 如果我们再脱离鸡蛋，回到这个数字，我们本来要做的事情是8加4，我们会把这个四变成什么呢？ 其实把这个四个鸡蛋拆成了两部分，第一部分是两个，第二部分还是两个。 第一部分的两个是拆到第二部分去的，是sorry，第一部部分的两个是放到前面那一盒里面的。 我们把这个八跟这个二是相加，拆这两个就是因为他们加出来是十，这个十出来以后，再把这个十和剩下的这个二再相。我现在画的是下面这个图，可能很多同学对我现在画的这个图很熟悉，但是我希望大家去理解到什么程度，理解到我在拆每一步的时候，你脑子里想象的是这个鸡蛋在怎么样变化，是这两个鸡蛋移过去的这个步骤，这两个鸡蛋移过去和这八个鸡蛋变成十个，十个也就是一盒。 类似的事情我们可以做很多次，比如说我再举一个例子，我刚才是8加4，现在如果我是比如说7加5，我可以怎么做呢？第一盒鸡蛋有七个，第二个鸡蛋有五个，那我会把这个五拆成三个和两个。为什么三个？ 因为第一盒还有三个空位，那么你把它拆成三个的时候，他在和第一盒的这个七个放一起，第一盒就满了。所以这两个一加就变十个，十个再加两个也变成12个。这里是家不是家，十个再加两个，它也会变成12。 这个过程，我们就称之为凑10。 什么叫凑十？就是我把两个加数，一个是八，一个是四，其中一个把这个四去拆开，拆成两个数的和，拆成哪两个数呢？拆成这两个二的和。为什么要拆成两个二的和呢？因为它能让第一个二去和前面这个八组成一个十，这个就叫做凑十。 那这个凑十实际上做了什么？他把20以内的加减法。特别是我们本来说要进位的加法。去变成了一个什么问题呢？通过凑十，本来是8加4变成10加2的问题，就变成一个十再加上自己的问题。我们一开始说的最最简单的那一类问题，那实际上它就相当于是我鸡蛋先买一盒放完再来看第二盒。 如果你要把这个凑十用的比较到位的话，你要对能够凑十的这个过程中，哪些东西能凑十，你要很清楚。这个也就是我们经常说的所谓的加法中的好朋友数。 我想我们很多同学应该经常做这一类数的分拆的练习，所以这一部分我不去详细展开了。但是我要说明一下，如果这些东西你不熟悉，那你一定要没事儿就给他练一练。 怎么练呢？不一定要通过做题练，比如说你家里有十个筷子，十根筷子你放好，或者你十个手指也可以。 然后你说十我能拆成什么？好，我把这个小指单独拎出来，这是一根，那那一根在这儿，剩下九根1加9，我一过去一个，这个时候就移过去，那边是两根，那剩下的还有八根。 当然我这个手描述的不太好，这里就是一根，那你这里就是九根346789。好，然后你把一根挪过去，9根里面挪一根过去，那么这里是两根，这里就剩8。 左边变两根，右边变八根。所以能凑十的无非就是这些数1加9 2加8。 你发现这个过程中再挪一个过去，左边的加了一个一，右边就要减一个一，左边加一，右边减一个一，5加5其实没了你再下去就是6加4 7加3这些。所以加法一共，拿这个个位数来说，就是这五组好朋友啊，五组好朋友所谓的1和92和83和74和65和5，这个就是加法的凑十法。 那么接下来我们来看减法，18减6等做这个特别的简单，因为我能把18减6想成什么呢？我们还是用这个鸡蛋来看。 我们一开始总是从最最简单的东西来看，18减68就表示一盒鸡蛋再多八个，这个就是十八。我再涂掉两个减6，就是你从中去掉六个，那我数一数，这个时候我会发现第二盒有八个，所以我直接从第二盒去掉6个123456，也就是我只要会算8减6，我就知道它是一盒多八个。 一盒加八个，那我把这个八个里面去减掉六个，去掉六个还剩两个。所以它就变成了一盒加两个。 也就是12，18减6等于12，这是最最简单的情况。 它简单在哪里？就是我整合的那一盒我是不要动的，我只要把后面那个八个里去掉六个就解决问题。但是如果我这个题稍微变一变，我们把它变成14减6。 那14减6就不一样了，哪里不一样呢？你看我现在还是一盒多四个，我把这里擦掉，这就表示14。那你去减6的时候，你会发现我第二盒我把四个拿光，我都不够减，我还要拿第一盒的。 所以这个时候就产生了两种办法处理。 第一种方法是什么呢？四个不够对吧？那我先把这四个拿掉先把这四个拿掉，我把这个复制下来。第一步他其实是这样一个过程，我们先用鸡蛋来理解。第一步把这四个去掉，先拿四个。 那第二步你还要拿几个你一共要拿六个。注意，一共要拿六个。六个你先拿了四个，那你还要再拿两个。所以第二步是干嘛呢？第二步你现在已经只剩十个了，只剩这一盒了，第二步再拿掉两个。 所以实际上你分了两步做这件事情。第一步叫做什么？把六拆成四个和2个，拿走四个，第二盒就没了。 第一盒里还要再拿两个，这就是我的第二步。这就是所谓的这种方法，我们他常常称之为叫做平时，什么意思呢？通过第一步以后，他把原来的问题转成了一个十减几的问题。 从这个数字上来看，我们是这样十四减6，这个六拆成四和2，于是你先十四去减一个四，十四先减一个四，减出来是10 10，再减一个二，那么剪出来98。 什么叫平时？一盒鸡蛋整的加一部分零的，先把零的去掉，只剩整盒的，整盒的就是十个，这个就叫做平时，其实它是分了两步。那么什么是破十呢？其实你就会发现，单单这个平时当然是一种很不错的做法。 所谓的破十就是我们现在要介绍的第二种方法。就是我除了可以，我还是把这个复制下来，把这一部分复制下来，还是看这个鸡蛋。我们刚才说我先把第二盒鸡蛋拿走四个，但是我们有同学说你第二个拿光也不够，我首先就把第一盒拆掉。你一开始那六个。 一开始的这个六个，我就先把它剪完，剪完以后变成什么样呢？剪完以后变成这样，请看啊这六个去掉。反正第一盒有十个，十个里去掉六个我肯定是能去的。所以他第一步是把它第一盒十个里拿掉六个，那么第一盒还剩几个？ 10减6还剩四个，所以它这个图是这样，14减6，先把十四给拆了。十四先把它拆成一个10和sorry，14减6，十四本质上它就是4和10这样两个东西。我把这个十去给它破开，用十去减这个6。好，十去减这个六减出来是4。 那到这个脑子里想着我的这个图什么意思？做到这里，这个四是什么？这个四就是这里剩下四个鸡蛋，那么我现在总共有多少个鸡蛋呢？就第一盒里还剩四个，第二盒里也有四个。所以再做第二步，就是再拿这个四根这个四相加，于是它也会等于8。 仔细体会一下平时和破时的区别。什么是平时一盒再加一些十个，一盒再加四个，我现在要去掉六个。平时就是先把散装的那四个去掉，我想一共要去掉六个，我就把六分成两部分，把去掉的分成两部分，这个六变成四和2，是去掉的分成两部分。 或者说拿走的分成两部分，这是平时一部分，拿完以后就变成了只剩一盒再要取几个的问题。 所以他先做一次，把那个减数，把那个六给它拆开拆成4盒，第二步再拿十件，而破十不那么麻烦。 说我干嘛我一共就减一次东西，我干嘛要剪两次呢？ 我一次性给它剪完，那一次性给它剪完，你从第二盒你是减不够减的，所以我就不用第二盒减一点，第一盒减一点，直接一次性用第一盒拆掉十个这个包装，把它拆了十个，完全解开，完全解开去掉六个，那么它还剩四个。 那这个时候它又变成了一个什么问题？它又变成了一个我们讲的第一个例子，或者说最简单的一位数加一位数的例子。你这里剩四个，这里还剩四个，那么这两个加一加该是多少就是多少，先是个减法，然后变成了一个加法的问题。 好，我们再来感受一下，再来感受一下。我们还是拿18减9来感受一下，我们用平时和这个破十分别试一下，现在我就不画鸡蛋了，但我们脑子里可以想象有一个鸡蛋18减9。我要在18个鸡蛋里拿走九个鸡蛋。 所谓平时就是先拿这个散装的里先拿掉一点，把这八个散装的去掉。那么这个9平时就是把它变成8和18个里十个加八个，先把八个拿掉。现在只剩一盒，在一盒里我还要再减去一个一，所以他就变成9。 先拿第二盒的散的再拆整盒的那破石是什么？就是平时迫使我一开始就把我这个十给它破开，整合的破开18，破成八个十个，我拿十去减这个9。大家一定要不停的做一种切换，一个是数字的，一个是脑子里的一个鸡蛋。 大家理解的时候，我是帮大家尽量用鸡蛋。但是慢慢的大家可以看到，我在逐步的把这个鸡蛋的图形给它抽掉，甚至可能大家在其他课堂也是给你鸡蛋。我也只是买一些圆圈来代表，再然后慢慢的你圆圈也不要了，你就完全看数字了。 好，10减90个1盒的去掉九个还剩一个。好，然后我要干嘛呢？一开始的整合的那个只剩一个了，但一开始的第二盒八个还都在，这八个我再和这个一一加还是一个九，所以18减9等于9。 其实你会发现，不管是凑十还是破时，还是平时，我们讲了三种办法，他有一个点是相通的。什么事情？他都是把未知的问题化为已知的问题。 为什么我都要找10？其实就是因为你会十加几的问题。因为我们是十进制，我们满十进10个就是完整的一盒。用鸡蛋来讲很简单，所以加法我就找好朋友去凑十。减法我就根据需要留出一个十或者拆掉一个十。平时其实就是留出一个十，那个破十就是拆掉一个十，把它变成几步和10有关的运算。 我们最后再来小小的做一个总结，来看一下这个凑十，我是把8加4换成什么？把四一旦拆开拆成2和2以后，8加4就变成了一个10加2的问题。这就是第一个未知化为已知，不懂的化成懂的，没学过的化成学过的。 18减6是本身就会的那14减6通过平时它化成了什么？化成了一个十减2的问题。通过破石它画成了什么？ 通过迫使它化成了一个4加4的问题，都是在把没学过的或者新的东西化为旧的东西，这也是我们数学学习的非常重要的一种方式。 好了，这一讲的内容我们讲到这里。但是计算这个事儿肯定是需要一定的练习的。 所以如果你已经读小学一二年级了，我想这一部分的练习你是要经每天做那么一些的那这个做不需要很大的量，理解这个意思，每天做个几道就够了，然后慢慢的就熟悉起来，甚至不一定要写下来算，你完全可以口算。在脑子里想象这个鸡蛋的过程，在脑子里想象这一部分的图形都可以。 好，这一讲内容我们就到这里，我们下一讲再见。 

# 06. 加减竖式运算

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是加减竖式运算。先把标题写在屏幕上，加紧竖式运算。 好，我们在第一讲中曾经学习过，我们计数的时候，在没有数字的时候，我们可以用在木片上画横线，或者用石头这种办法来解决。比如说我要知道我有多少只羊，我可以在一个木片上一条线表示一只羊，一只、两只、三只、四只这么一直画下去。 但是后来我们会发现，这样画有很多很多羊很麻烦。所以我们在之前提出过一种想法，说我用一个圆圈表示十条横线，也就是用一个圆圈表示。 十条横线也就是用一个圆圈来表示十只羊。这个是在没有我们数位的概念下，我就得用不同的符号来做表示。 但是后面我们学到了说，我们可以有个位数，有十位数，那我其实就可以只用横线不用圆圈来表示。这种想法在我们现在写两位数的时候已经是这样写了。 但我们还是可以回到古代，我们想一想古代我想表示爸爸有15只羊，我可以怎么表示？没有数位。我们之前学了我可以用一个圈加五条横线，或者我直接就画15条横线。 12345，这种方法也可以。但是其实古人也想到了一种类似的办法，他说我用两块木块来表示15只羊，什么叫两块木块呢？我指的是在两块木块上去划横线，第一个木块上我划五道，这块木块上我们说每道表示每条横线。 表示一只羊。 他再拿一个木片，再拿一个木片，这个木片它比较值钱了。在这个木片上它现在画一条横线，但是它在这个木片上，它是每条横线表示。 两表示十只羊。他只要这两个木片的位置一直放的是对的，没有被人调过，我们就不会搞错数字。 这其实就是我们后面用的叫做个位和。十位只是在以前，我们没有这个个位十位，我们只能用两个木片。 那有两个木片还是有麻烦的，一不小心你两个木片的位置换一换，哪天有个小调皮鬼把这个移到了这儿，那这样一旦移过去以后，十五就变成了51，马上就出问题了。 那么这个东西和我们要说的这个加法竖式有什么关系呢？其实我们可以再想象一个问题，爸爸有15只羊，那妈妈可能也有一些羊。比如说妈妈有21只羊，我们还是可以这样来表示十位各位，十位21支，这里就是两条线，个位就是一条线。 同学们可能会说，老师我早就会用竖式了，为什么你还要用这么一条条线来帮我理解？其实这个才是最古老的时候我们的一种直接的想法。竖式说到底就是用我们现在说的这种想法所引出来的一种计算方法。 好，这是妈妈的，下面的是妈妈的，上面的是爸爸的那现在我想知道我们总共有多少，我们可以这样，我还是拿两个木片。好爸爸在这个个位上有五条，先把爸爸的写上去。 1234 50位上有一条，也就是这个表示十只，这个表示五只。那妈妈在个位上有一条，我不用红的，我用蓝的，在十位上有两个，以前我可以划线，也可以用打绳结，没关系，反正只要两片木片我就能这样了。 这样一数我就会发现，其实相当于这个就是十位，这个就是个位。其实十位就对应着三个位，对应着6，看起来是在数数，实际上我们就是在做加法的一种运算。 画横线太麻烦了，所以我们把它变得再简单一点，就是用作了数字。我们看一看这个六是哪来对，这个六其实就是爸爸的个位上的五和妈妈的个位上的一啊妈妈是21只。 我们来看一看，这个六其实是这个五和这个一所加出来的那这个三是什么呢？这个三是爸爸的十位上的一和妈妈的十位上的2加出来的。 所以如果我把这个事情用纯数字去表达，他就会变这样。爸爸是这样，15，慢慢是21。 好，这里我写一个加号，表示它们相加的一条横线。五和11加得到61和21加得到3，总共36只羊就是这样出来的那这个式子我们就叫它竖式，为什么叫竖式呢？ 因为我们是竖着在进行计算的，所以我们就管它叫做竖式，竖着计算式子叫做竖式。我们再来感受一下，比如说我们来算一算12加23，你看我这么写着的时候，是横着写12加23内容竖式12、23相加。 好在相加的时候我们一定要注意对齐，什么对齐呢？个位和个位对齐，二和三对齐，十位和10位对齐，一和二对齐。为什么一定要个位和个位对齐？ 因为你拿这个2加这个三的时候，这个二就表示两个一，这个三就表示三个一。如果是阳的话，这个二表示两个羊，那个三表示三个羊。那两个羊和三个羊放在一起，我零散的就是五个羊在外面。 而十位的一和2为什么要对齐？因为十位的一和2，这个一表示十只羊，二表示20只羊。 所以它看起来是一加2，实际上是10加2 10只是后面那个零，我把它省掉了，所以这个一加2加出来是3，这个三真正的意义是一个30个位和个位对齐，十位和10位对齐。这可能是数值运算中最最重要的一个东西，就叫做对齐。 不仅两位数和两位数加是这样，以后我们碰到更多的数位都是这样。 好，这是加法的数据运算。那加法这么算，减法也可以这么算。比如我们家刚才我们算了总共有36只羊，我现在要送给邻居15只好。如果我还是用这个木片，36支就是123。这里六画六条线，123456，这是个位，这是十位。我们来体会一下过程，我送给了邻居15只，送出去15只。 36只送出去15只还剩几只？那送出去15只就是这里划掉，只剩最上面那一个了，这里划掉一个，那我一看个位上还剩一个，其实就相当于我们之前说鸡蛋也是这样，三盒鸡蛋加零碎的六个，现在送出去一盒加五个，所以我还剩几盒？ 十位上本来三盒，现在变成了两盒送出去了一盒，个位上六个送出去了五个，还剩一个。所以这件事情，你从这个画横线的角度是我给展示的这样一个过程，但我也完全可以用竖式来做。 30 65，现在我们是减法，所以我们这里写个几好个位6和个位的5去减，我们也要个位和个位对齐，为什么？因为它表示这个66只，这个55只，这个一减出来是一只十位，三和一去减。 这个个位表示sorry，这个十位，这个十位表示30只和10只，看起来是三合一，它代表的是三个十和一个十，够十位减出来是一个二，所以36减15就等于21，这就是我们的结果。如果我不是送出去15只，有可能我说我送给邻居五只，那么它就是36减5。 那36减5，其实我们从这个木块上来看是这样，123，这里是123456，这是各位木块上如果画横线。十位，那你减5，其实只在这里去掉了五个，我们当然也看得出还剩31个。 我们在列竖式的时候，我们会这样列31减5。那五的十位没有东西，这个我们是不用去给它补零的，直接就不写就好了。 36，对不起，不是三十一。然后老样子，个位和个位对齐，十位和10位对齐。那么求出来这里我们来看个位6减5，还剩一个一。十位三直接挪下来就变成3。 为什么直接挪下来？因为它是3减0，所以其实它就没有被减过。那我直接挪下来就好了，还是做对齐好列加减竖式运算的时候，我们一定强调最最重要的两个事。不管是加法还是减法的竖式运算，只要是竖式的加减法，我们就一定要注意两个字，对齐。 为什么要对齐？这是更重要的一个问题。它来自于你个位上的数是什么意思，十位上的数是什么意思？ 你把个位的2和10位的三相加，那是没有意义的事情。就你两盒鸡蛋直接加三个，这个2加3代表什么呢？没有意义，它不是加出来一个五好，那么除了要对齐，我们还要记得这个运算符号不能漏掉。 这里是一个减号，前面的加法你就要写一个加号，那以后乘除法你就要写乘号和除号。 接下去我们看一些更为复杂的竖式运算。25加47还是用数字竖式来进行计算。好，25我们还是给它对齐十位、二个位、50位、四个位7相加。好，我们各位先对齐5和7加出来是多少，然后十位2和4看加出来是多少。那么在个位5跟71加，你就会发现，跟刚才不大一样，5和71加是12了。 这个12我可不能在这个黄圈，大家看啊，我不能在这里填个12，这里一填十2，不管我前面比如说我再填个6，那都变到这么一个像六百多的数了，不可能。个位它就是一个位置，一个位置我就只放一个数，不可能放两个数。 那算出来是十二意味着什么？12其实可以理解成是12个1。 五个一和7个亿加12个亿，那你其实12个1相当于什么？在十进制下我们是要满十进一的。 十进制下满十个我们就进一个了，已经能组成一个新的一个盒一盒东西了。所以12个1又相当于是一个十。 两个一，那十的东西我扔到十位去就好了，所以在这里5加72，但我在个位只会写一个二，但写这个二的时候我得记着，我多了一个十，这个十其实是十位里的一个一。 这个十在个位，在你一个个数的时候，你觉得是个十，但是当你把它放到10位上的时候，它就不过是个一了。 那么我为了不忘掉这个东西，我们有时候会在这里写一个小小的一，表示我敬畏，敬上去了一个一进位。 像我们之前很早时候做4加8，四个空的鸡蛋，八个空的鸡蛋，84个鸡蛋八个鸡蛋。那么我把四个里面挪几个，挪到那个八个，就把那一盒给凑满了，其实是一样的道理。 那么现在再来算十位，2加4本来是6，其实这个2加4是20加40，是六十。那么你这个6，别忘了，这个六代表60，前面又进上来一个一，这个一又代表一个十，所以你的十位其实就变成了一个七。 这个七是哪来的？这个七其实是2加4加1加出来的，这个一是进位进上来。 近未来的一个亿，这个就是竖式运算中要进位的情况。满十进一在这里写一个小小的一，前面给他进上去，在算十位的时候不要漏掉这个。 一好，我们可以再来一个，感受一下竖式运算，16加25，16 1625写加法。好，6加5 6加5，我们知道是十一，但是我个位上只能写一个一。个位上我是不能写11的，所以这里写个一。但是我知道我进了一个一，进在这里再算十位的时候，一和二一加本来是3，再加进上来的一就变成了4。 好，注意在加法竖式运算的时候，我们前面讲过几个重要的事儿。第一个我们说要竖式对齐，在这里我们要再加一个，加两个数位对齐，不是竖式。对不起，个位和个位对齐，十位和10位对齐。 接下去我们注意我到底是先算蓝的还是先算黄的那比如说在前面这个题里，这个题里你先算蓝的先算黄的，是互相不影响。12 1加2 32加35，先算蓝的，先算黄的都可以。 但是在下面这个例子里你就会发现，我先算蓝的2加4是6。但你写完了以后，待会儿你再算个位的时候，个位有可能要影响十位。 那我先算十位，先算蓝的就不合算了，因为你不算完，可能要改。那我能不改能少改，我当然就不改和少改。 所以我们会先算它的个位，个位算出来如果要进位，那我十位的时候就进上，不进位那我十位就正常算。 这就是我们的数加法竖式的第二个原则，叫做从低位算到高位。低位一共就两位，所以这里的第一位其实指的就是个位，这里的高位其实指的就是十位。第三个，我们说如果满十，我们就进一个一。这就是加法的竖式运算，两位数会算的，以后不管到几位数原理都是一样的。 那么我们再来看稍微复杂一点的减法的竖式运算，61减24。好，这里写个减号。同样的你个位要减个位，十位要减十位。但是我们会发现现在碰到的问题是什么？1减4在个位上。你是不够减的，你没法解释。那怎么办？ 其实61减24相当于什么？我有六盒，每盒装十个的鸡蛋，还有一个鸡蛋零碎的装在外面。24是什么？两盒整的鸡蛋，还有四个鸡蛋零碎的装在外面。 那你零碎的装在外面，你说61比24多少？那个一比那个四的确没有多，没办法做。所以你真的要看61比24多少的时候，其实你是要拆掉。就像我们上一讲的，平时也罢，破十也罢，对吧？ 其实这里相当于是一个破石，我需要把61里6盒加一个，我要拆一盒出来，然后才能给你去减那个个位的41。没法直接减4，得把十个的那一盒再拆掉一些。 那么我去拆掉的时候，实际上就相当于我把一减四改成了先把61看成一个，有五盒完整的。本来是6盒完整的加一个，相当于是六盒鸡蛋完整的再加一个。 那么我现在把它看成是这样，五盒完整的再加11个。为什么要11个呢？ 因为11个才够去减它那个4，把它变成11减4，那11减4等于7，这个减的时候我要记得我会在这里做一个小点点。 我这个本来是1减4，我问十位借了一个。一借了这个一，注意这个一是一个十位的一。其实是10。 借来的可是一盒而不是一个，所以11减47。好，那我们把七放在这里。那么十位上本来六盒有一盒已经被借到前面去了，我十位只剩五盒了，所以这个时候的6减2不是6减2，我点这个点就相当于记了个账，我这里要扣一点东西了，它实际上就已经只剩五了。 所以这个减出来，是5减2等于37。这个三哪来的？是6减1减2，这个减1，就是被借走的。 一定注意这个一可是一个十，它借到个位上的时候已经被当做十在用了。个位上我最后可不是1减4，也不是1加1减4，而是1加10减41减4。这个就叫做借10位上的一个一借到个位上的时候，我们是当成十了，实际上它是10，所以叫借一当十，再来一个。47减39，47姐39。 好，个位管个位，十位管十位。这一讲我还是帮大家先把颜色都这么标上，把这个对标这个颜色。为了给大家强调这个对齐，7减9不够减，那怎么办？借一个17减9等于8，那17减9怎么减？我们在前几讲已经讲的很明白了。 好，那么你这里借了个一，所以这里要标一个小点点，记一个账，记一个账。好，那么七减九变十七减9，这里是8。 注意十位上本来是4减3 4，被借走了一个三，所以这里其实是4减1减3，没了，一个都没了，那你就不要写0，因为第一位我是不能写零的，我们不会写08，我们就写8。所以47减39就等于，我们想写一个结论，就是47减39就等于。 好，到这里，减法的竖式我们也比较快的过去。我认为加法你理解的减法是同样的道理。当然这个事情是需要很多的训练才能保证你不出错，刚才我们说加法竖式写了三个原则的数位对齐，先低位、在高位、满十进一。类似的我们把它copy下来，我们看看减法竖式三句话是什么？减法数式这个就减法了。 数位对齐一样，先低位在高位也一样，但是它不叫满十进一，而叫什么呢？借一。到10满10进一，在加法里是个位上满十个，进一进到十位上十个变一了，十个一变一个十。满十进一本质上是十个一变一个十，借一当十本质上是什么？是借一个10到10个一。 其实我们在之前说过，加法和减法就是互相反过来的一个过程。那你十个亿变一个十，反过来就是一个十变十个亿，它还是同样的道理。好了，这一讲的内容我们就讲到这里，我们下一讲再见。 

# 07. 数字谜基础 （一）

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是数字迷基础的第一讲。我先把标题写在屏幕上，数字谜。什么叫数字谜呢？ 和数字有关的谜语差不多就是这个意思。他其实就是在一个算式里面少掉了其中的一部分的数，或者少掉了一部分的数字。请你把这个算式填充完整。这样的问题简单的很简单，难的可以一路变得非常的难。 在我们的这个课程里面，我们会接触到其中的一部分的问题。会从数字迷的基础，慢慢的我们学习到稍微进阶一点的数字迷。 但是更难的数字迷的问题，我们是不会在启蒙阶段去做设计的，他通常需要更多的其他知识的配合。既然上一讲我们学到了竖式的运算，我们不妨就把这个数字迷的问题从竖式里面来做一个开头，来做一个开始。 那么在竖式的运算里面，我们在上一讲中已经学到说有三个原则。 不知道大家是不是还记得竖式运算的三个原则。第一个我们叫做要数位要对齐。第二个我们先算低位再算高位，如果这个已经不记得了，一定再问一问自己，为什么是先低位在高位？ 它的好处在哪里？这个东西你理解它比理解算怎么算竖式更加重要。第三个就是在加法的时候我们会满十进一，在减法的时候我们会借一当十。 这两个过程其实是一回事情，只不过是顺着看还是反着看的问题。 在竖式运算里面，如果给我两个加数或者一两个数去减一个被减数一个减数，我们就能把结果求出来。那么现在我们碰到的问题，其实又是某种还原。 什么叫还原呢？就是给了我一部分的东西，但是我要把剩下的给它填出来。 那我们想具体怎么样去做呢？比如第一个问题，我们这一类都是填数字，使这个数式成立。 其实我只要想竖式是怎么算的，反过来来讲这个问题，当做加法竖式的时候，我们知道我们是先算个位再算十位，个位和个位对齐，十位和10位对齐的。现在好，那么四加某个东西这里是8。 其实我们知道如果是四和3，那这里比如会出现个7，但是如果说是四和9，你哪怕加出来是十三，其实这里也会出现一个三，只不过你那边会建一个一。所以这个地方出现8，理论上是两种可能，两个数相加个位，两个数相加出来个8，理论上有两种可能，哪两种可能呢？ 可能性一。其实就是等于8，可能二这两个数加出来，其实本来是多少？ 是十八，然后这里留了一个八，然后满十进一，那个十进到前面去了。但是我们注意到它是4加某个东西，4加1个东西等于8。 注意，而且它加的是一个数字，一个数位，加的是个一位数。 4加1位数等于8，那这个地方是四有可能4加1个一位数等于18，那是绝对做不到的事情。所以这个可能28我们就把它去掉了，没有这种可能。 现在只有一种可能，就是这个地方填个4。那么如果这里是四的话，4加1个东西等于8，只有4加4。 那么前面三和2，注意这里没有进位，所以三和二这里一加就是5，所以我们这样就把这个竖式给它填完整了。 这个地方为什么是4？其实我想的是四加几等于8，那我可以很快的过一下，一位数加一位数是我们最熟悉的4加1 4加2 4加3 4加4。当然还有一种办法是把它转成四加几等于8，也就是8减4等于几。 这个理解会更难一点。虽然我们前面有涉及过，但是我们待会儿再来详细展开这种理解。现在我们大不了你先想，就自己一个个去列，四加几等于8，你应该也是知道的。 好，那么这个是最简单的一个问题，在这个问题里没有任何的敬畏。 我们来看一个稍微复杂一点的，还是填数字，实数是成立，其他的没变，这里还是32。这里唯一和刚才那个题有变化的，就是下面这个地方，刚才是8，现在变成了一个三。 那么这个三，其实我知道两个个位，就两个一位数相加，如果在竖式里出现一个三，理论上我也是两种可能。可能一其实加出来是3，可能2加出来是十三。 但是你想想，4加1个东西等于三是不可能的，它只能等于13。所以也就意味着这个地方我不管四填什么，我都不可能4加1个东西正好等于3。 那他最后为什么是三呢？说明有进位进到前面去了。这里本来是十三四加几等于13呢？4加9等于13，并且这里进位进了一个一，那么这里进了一个一，十位上，本来是3加2，还要再加一个一，所以这里是6。 接着我们再来看第三个问题。前数字是数字成使数式成立。好，老样子，各位和各位对齐，十位和10位对齐。我们分开看。 个位理论上这里加出了一个三，所以这个三本来是有两种可能。第一种可能。可能一加出来就是3。第二种可能，其实这里两个个位上的10加出来其实是一个十三。 那个一进到前面去，但是我们注意到这边有一个七，一个个位上的数是7，那一个框框里的数加上7，一位数加七等于三是不可能的，所以它不会是3，他只可能加出来是十三。 有一个一满十进一了，进到前面去了，这个十进到前面了，那么谁加七是13呢？只有6加7是十三，注意这里一定进每月1。那么这个十位这个数是多少呢？ 十位这个数其实十位我不知道的这这玩意就是一一加上这个框框里的数，我给打个问号，再进了一个一是等于4。好，你一加这个东西再进了一再加了个一才是四。那你那个一不加就进位，没有的话就只有三一加谁等于三呢？ 1加2等于3，所以这里应该是2，16加27等于43。 那么加法的问题，我们就看到这里，接下来我们来看减法的问题。填数字使竖式成立，85减60多少减六十几，等于这个框框。三好，那我们现在老样子，减法还是从低往高位数位对齐。但是我们要注意，它有可能是要从前面借位的各位无解某个东西这里填上3。 如果我不管前面各位是谁减谁的，但是我也能我就知道这个三其实也是两种可能。可能一是什么呢？ 可能一是五减这个框框减出来是个三，可能二我说的是第一个框框，可能二是什么呢？其实是15减这个框框等于3，也就是从前面借过一个位，不是单单五来减的。 但这个框框是一位数，所以5减1个东西等于三是有可能的。 五减几等于3 5减2等于3，那15减1个一位数是三，不可能。15减1个一位数怎么着也要比五要大。那你15减10才只有5。对，这个是不可能的。它一定没有发生过向前的借位，所以这里是5减2等于3，没有借过位。那前面8减6也是二，这两个二我们就轻轻松松填上去了。 好，再看最后一个舒适的数字谜的问题，54减多少等于9？其实这个九它怎么来的？它要么是四减一个框框，我还是写可能一，要么就是四减这个框框等于9，这个不可能。可能2？其实是借借过一个位，借一当十了，是十四减一个框框等于9，这个有可能，第一个不可能，第二个有可能。 那14减几等于九呢？你可以一个一个试了，或者你用这个减法的关系也可以，14减5等于9，所以这里是5。但一定注意前面借过位了，这个点点上去记一个账。好，于是前面就是再算前面这个地方的话，就是五倍借了个一，还剩44再减3，那这里就是一，所以整个式子其实54减35等于19。 其实我们做一个简单的总结，你就会发现这种我们刚才到现在为止的问题，我们都叫做简单的加减法的竖式的数字谜。竖式的数字名。 其实没什么东西，他只不过是什么，只不过是你只要知道竖式运算的原理。你正常的从低位往高位算，注意这个老样子还是低位往高位算。 你要特别小心一个事情，就是到底有没有进过位，有没有退过位。有没有过满十进一，有没有过借一当十把，这个事儿想明白就可以了。 它本质上还是竖式运算的原理，只不过一个是正着算，一个就是你要想它这个东西是怎么出来的，反过来去思考并没有什么大的区别。 好，这是我们这一讲的第一部分内容。然后我们来看这一讲的第二部分内容，来看一些小小的韩士尼，横着的他问三角代表的数是多少才能使计算结果正确？当然这个题非常简单，三加多少等于20？其实我们前面已经用到了一些这种解决这种问题的解决方法。 现在想帮大家看的再深一点，三加多少等于20？我们回到实际的意义，就是三个苹果加几个苹果等于20个苹果，三个圈圈加几个圈圈等于20个圈圈。好，那实际上就是这里是三个，你总共是20个，我花20个，1 2 3 4 5 6 7 8 9 10 1，2 3 4 5 6 7 8 9 10。 什么叫三加多少等于20呢？其实就是你把这20个东西去干嘛，去拆成两部分，一部分是三，三还要补多少个才能补成20，那这就是剩下的。 其实补多少个呢？1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17，我这个时期是怎么来的？ 其实就是你看在原来的图里，本来是20个在一块儿，现在问三加几等于20？就是二十拆成两部分，一部分是三，那还有一部分是多少？ 那还有一部分不就是20去掉了这个三所剩下的吗？那其实就是17个。 所以这里最重要的一件事情是什么？就是三加多少等于20，其实就是问这个三角就等于了二十节3。 这个在最开始理解加减法的时候，我们也说过，三加几等于20。我一种理解就是从三开始数数，往后数几个能数到20。 那你从三开始往后数几个数到20，就是从20开始往前倒着数几个能数到3，那二十倒着数几个能数到3，这就是20减几等于3，也就是20减3等于几的一个问题。 这个是从数数的角度来看，现在右边这个图其实是从什么？其实是带大家从整体与部分的角度来看。什么叫家呀？家就是合并。家就是合并，当然它会增大会变大。 那减就是什么？减就是分拆把它分开，20减个三，那也就是20去掉三一部分是三把这一部分去掉，剩下那部分是多少？所以分拆当然是减少，剩下那部分是17。那么三和多少拼起来是20？就是你把二十一拆二，一部分拆成了3，剩下那一部分是多少？这是同一个问题。 那你把20分成两部分，一部分是三，还有一部分是多少？那不就是20减3，所以我们一定要理解，3加某个东西等于20，其实和这几个东西等于20减3是完全一个道理。 那么20减3，当然我们知道它是等于17，所以这个三角形就表示17。好，加法会变大，减法会变小。 其实我们在这个问题里，我们去识别这个加一加以后，这个是最大的一个数，这两个都是小的数，两个小的数一加变成了个大的数，其中一个小的数就是那个大的减另外一个小的。 再来看12减多少等于7，那12减多少等于7？同样的了，我可以这么写，1 2 3 4 5 6 7 8 9 10 11 12，本来是12个，我要减掉多少等于七呢？ 其实就把它拆成两块，一部分就是余下的71234567，另外一部分就是我不知道的要减掉多少才能拼起来是十二，那一样我十二减这个等于7，十2其实就是最大的一个数。这个是分成了两个小部分。 当然现在我们很清楚，12减多少呢？12减5，因为这个数字我给的非常的小，但我还是希望大家能看右边这个图，在右边这个图里是非常清晰的。 什么叫十二减多少等于7？就是你12里拿出来几个，把12分成两部分，一部分拿出来多少，这是我不知道的那另外一部分17它相当于在问你，你看我先拿左边这些剩下七个和我先拿右边七个剩下左边是一个道理。 其实我先拿右边剩下多少是最简单的，12个0，拿掉七个，这个问题对我比较好解决。12减多少等于七呢？ 但12减7等于多少方便。可是实际上总之十二是大的，这个三角和这个七是小的。 其实就这两个加起来是十二，12被拆成了两部分，那么你要算其中一部分，任何一部分小的都是拿大的减去另外一个小的，所以这件事情就是在说三角等于12减7，那么十二减七我们当然很轻松可以算出来。它就是，这种问题本质上是对加减法意义的理解，对加减法意义的理解就可以把这种问题轻松解决掉。 好，再来看一个例子，我们现在说不止一个东西了，有圆圈有三角，求三角和圆圈代表的数。 但是我要使下面这两个算式都正确，都成立。 注意我三角表示的是同一个数，这个三角跟这个三角表示同一个数。 这个和前面这个竖式谜的时候不一样，竖式迷的时候，比如这个问题里，我这个方框跟这个方框里可以是不同的数。但在现在我们这种图形里面，我们现在是默认说这个三角和这个三角它代表的一定是同一个数，圆圈跟三角可以是不一样的，三角和三角形是一样的，它不是一个框框，前面我们是一个框框，表示我不知道里面是谁。 好，那么现在有两个式子，第一个是它，第二个是它。我们先想想哪个式子更简单。毫无疑问我们同学都会说一定是这个更简单。 我写个E表示简单，上面这个是更难一点，为什么？因为上面这个你又有圈圈又有三角，你有两个以上两个图形以后，我们叫这个叫未知数。你有两个不知道的肯定比一个不知道的要麻烦，所以现在我肯定是找这个东西作为突破口。 它加四等于8。好，我们知道，那八就是大的那个对吧？这两个小的加起来等于一个大的那小的这个就是大的那一部分。那八里面一部分是四，另外一部分是几呢？那另外一部分就是去掉4以后，把所剩下的，所以就是8减4等于4。 如果直接对数字觉得这个难，就把它想成鸡蛋，想成圆圈。反正就是一个东西一共有那么多个圆圈，这里圆圈用掉了，那你就想成鸡蛋。一共有那么多个鸡蛋，有八个鸡蛋，几个鸡蛋加四个鸡蛋等于八个鸡蛋？它相当于说我八个鸡蛋分成两部分，一部分是四个，还有一部分是多少，就是这么一个问题。 好，那么三角是四以后，我再把它带到最前面那个圆圈加三角等于十5。现在我已经知道三角是四了，对不对？那我就把这个四放进去，所以其实就是圆圈加上四等于15。那跟刚才一样道理，圆圈就等于15减4，所以圆圈就等于11。 接着我们再来看这一讲的最后一个问题。和刚才类似，求三角和圆圈代表的数是下面两个计算结果都正确。但是现在我们会发现在刚才这个问题里，我第一个式子是有一个圆圈一个三角，有两个图两个符号。 第二个式子只有一个三角，第二个更简单。现在这个这里是圆圈加0.32元个东西，这里三角加三角也是两个东西。但是注意不是说我看到底有几个符号，几个图形，我看的是什么。 第一个算式里又有圆圈又有三角，它有两个我不知道了。第二个里其实只有一个，我不知道的就是三角没有圆圈了。所以同样它还有难，可以谁简单呢？ 第二个简单它易，第一个很难，所以我从第二个入手，三角加三角等于6，自己加自己是6，那我能是几？一加一是2 2加2是4，3加3才是6。 所以我马上就知道三角等于3。这个时候再来看第一个式子，圆圈加三角等于15，那三角等于3，我把它带到这里去，三角已经是三了，那这个三角我就可以写成三好，所以其实就是圆圈加三等于十5。 那圆圈是小的一个三十小的一个十五是大的一个。 这个整体是这两个部分加起来，所以任何一个部分这个圆圈就是15，再减去这个3，所以圆圈就等于15减3，圆圈等于12，所以有两个以上的两个或者以上的这种图形的这种数字谜，我们要找突破口，其实就找哪个简单就从哪突破，这是非常容易想到的事儿。那什么叫简单呢？ 你在某个式子里就有一个我不知道的那肯定比两个不知道的更加简单。好，这一讲内容我们就讲到这里，我们下一讲再见。 

# 08. 加减巧算 （一）

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是加减法巧算的第一节。我先把标题写在屏幕上，加减法巧算第一讲我们之前已经学习了一些计算的方式，比如说竖式运算等等。在这一讲中，我们牵涉到了第一次，在我们这个课程中牵涉到了巧算。 什么叫巧算呢？就是巧妙的计算。那为什么要巧妙的计算呢？ 其实是为了提升我们计算的速度。在我们刚开始学计算的时候，我们碰到的计算问题都比较简单，计算量也比较小。可是随着我们的这个学习的内容逐渐的加多，我们会碰到越来越大的这种计算量。 很多时候通过一些更好的方法，我们可以让我们算的更快一点。 但是我们一定要知道，在学巧算的时候，其实我们始终关心两个点。第一个点是怎么算更快。第二个点是。为什么能这么算？那么为什么能这么算呢？其实通常就是计算的道理。我们简称为算力。 算力很多时候比具体的计算更加重要。这样一种观点是很多人所不具备的，我们会发现有很多很多人他做了很多很多的计算题，但是他的这个计算能力就是上不去。主要原因就是他一直没理解为什么能这么算学数学理解为什么始终比怎么做更加重要。 我们先来看第一个例子，我们说计算这样两个式子，4加15加16和4加16加15。我们首先问两个算式的结果是不是相同，我们可以来算一算，4加15加16，可能各个同学的计算能力不一样，4加15是19再加16。 有些同学19加16已经可以口算了，有些同学还要列竖式，没有关系，反正你可以用你自己的办法去算。那算出来结果应该是15。 那么4加16加15这个是4加16是20，20加15，13 15。我们把这两个自己都去算一算，那算出来以后，发现结果是相同的。 但是我们这边主要要问一个问题，就是为什么相同？我们同学说我就把位置换一换，不会影响这个计算的结果。加法位置可以换。或者我要问的你更明白一点，为什么我能换？ 其实你可以回到加法的意义上来，4加15加16，你可以想象成爸爸有四个鸡蛋，妈妈有15个鸡蛋，你有16个鸡蛋。那你们一家三个人加起来一共有多少个鸡蛋？先把爸爸妈妈的放在一起，那就是4加15，再把你的放进去，那就是再加16，这就是左边这个算式，爸爸妈妈先放一块儿，再把你的放进去。 那右边这个什么意思呢？4加16加15就是先放先把你的和爸爸的放在一起，你是16个，爸爸四个对吧？4加16，然后再把妈妈的放在一起。 那你想你想象一种实际的这种情况，每人有一些鸡蛋，先把爸爸妈妈的放一块，再把你的放进去，和先把你和爸爸的放一块儿，再把满满的放进去。你说加出来结果是不是一定相同？这个其实就是因为加法就是合并，就是把一些东西合在一起。 那合在一起的过程中，我先和哪个再和哪个，这个并不影响。 好，那么我们理解它的结果为什么相同以后，我们再来感受一下。我问大家，大家在算第一个的时候快，还是在算第二个的时候快？ 第一步的运算应该差不多，4加15和4加16你的计算速度应该差不多。但是你算19加16和20加15的时候，一定是20加15更快。 用我们最早的鸡蛋来说，20加15就是一个是两盒，一个是一盒多五个，那就是三盒多五个，就是35。 19加16是1盒多九个和一盒多六个，你计算的时候会麻烦，如果用竖式运算还得进位。那这个为什么能更快呢？其实是因为我这里有20这个数，这个数我们叫做整十的数，12 13 10都是整十的，整十的数加上一个数比较好算，你20加15肯定比21加15，26加15要好算。 所以在加法的运算中，我们经常有一种办法来提速，怎么来提速呢？我们就叫做凑整。什么是凑整？就是凑一个整十的数，那通过什么凑整呢？通过就是个位上的好朋友。什么意思？你看这个20怎么来的？这个20是不是4加16加出来的？ 4加16为什么加出了个美妙的结果？ 因为四的个位是46的个位是6，我们知道4和6是啊好朋友，所以我们通过个位上是好朋友的两个数相加行凑诊得到凑整，就得到了一个和为十的，sorry，就得到了一个整十的数。 那一个整十的数再加剩下的数，我的运算就可以变得更快，这个就叫做凑整来加速我的运算，凑整来巧算。 有这个想法，我们又知道我们可以去换它的这个加法的运算顺序，先加哪个再加哪个不影响结果。 那我们就可以以后看到4加15加16的时候，我们就会这样子去想，先观察，一定注意先观察。 你发现四十六好朋友好，那我就先算什么，就把它先算4加16再加15，那这样就是20加15等于35，我的计算速度就提上去了，你可以自己去感受一下，我再给你一个例题。 12加21加18，当然我们可以直接算，但是经过了刚才的讨论，你还是可以先直接算一算。然后你再比较一下，如果我们先观察再凑整能不能更快。 现在三个个位数，一个是二，一个是一，一个是八。我们会很敏感的发现八和这个二它是能够凑成一个整十的。 因为二和8是好朋友，所以我们怎么样算比较快呢？我们就说12加21加18，先算哪两个？ 先算12加18再加21，12加18算出来一定是整十的数，所以它就是30，30再加21，那那它就是了51。那这个计算速度我们就快得多了，这是我们今天想介绍的第一种办法，在加法中通过个位上的好朋友来进行加法上的凑整。 接着我们再来看第二种巧算的方式，我们来看5减4加3减2，这个怎么算？我们同学说很简单，我会算是的，你一定会算5减4加3减2，我有第一种理解。第一种理解就是如果从计算上，我就是一步一步算5减41，它就等于1加3减2，那1加3就是4，那就等于4减2 4减2是2，这个我们很熟悉。 那这个对应的理解是什么？就比如我们还是想象成鸡蛋，我一开始原本有五个鸡蛋。拿走四个。 再有人在增加了。 或者说再补了，用补，反正就是往里面加。我一开始有五个鸡蛋，被人拿走四个，然后谁又给了我三个，再补了三个，又被拿走两个，剩下多少？ 这是不是就是5减4加3减2的1个加减法的一个很简单的理解。好，这是我们第一种直接算的理解。 那么我是不是还能这样子去想，我去看整个的过程，我把它分成两部分。原本有的甚至你可以理解成原本有五个，你也可以想成原本没有，但有人给了你五个。 没关系，反正这五个是加的，又补了三个，这个都是在让他怎么样变多。红色的其实都是在加，都是在变多。 而蓝色的拿走四个，再拿走两个，实际上它都是在变少，被别人拿走。那你想，我有五个被人拿走四个，再补了三个，再被拿走两个，拿拿补补拿拿补补。另外一种情况，我说我先。 拿过来五个和三个，一共补了多少？所有加的部分是。五个和三个所有减的部分，也就是所有被拿掉的部分。 一共人家拿了两次，一次拿了四个，一次拿了两个，所以减的部分是四和2。 好，那我们知道，所以一共加了多少？一共加了八个。一共减了多少？被拿走了是4加2等于六个。好，那我最终有多少？所有加的减去所有被拿走的就是我剩下的。所以最终这个答案我也可以这么算，所以最终其实5减4加3减2。 就等于8减6等于2，这种想法是什么呢？就是把加的放在一起算。放一起放。 这个方案一起算的时候，其实都是你加了几次，每次多少，全部加起来减的，也放一起算。所有减的也给它加起来，先减掉四个再减2个，那实际上就是减了六个。这个放一起算的时候我们用的是加法，这个加的放一起算的时候我们也用的是加法。 我们注意，这里两次都用了一个加法。 这里为什么是加？就是我一共补了多少，我一共拿走了多少，每次是这么多，所以要加起来。但至少我们会发现它背后用这两种算法算出来结果是一样，是因为我改变拿和放的次序，补和拿走的次序是不影响结果的。我这个运算，我们叫做又有加又有减混在一起，所以我们常常叫加减的混合运算。 加减的混合运算的时候，我就可以先把要加的都加起来，再把要减的也都加起来，再难加的跟减的拼一下。 你看所有要加的是5和3加起来是8，所以要减的是四和2，所以一共要减掉6，再拿要加的减去，要减的8减6等于2。 混合运算的时候加减混合运算先把要加的加起来，再把要减的也加起来，这样让它减一减，和你一步一步加加减减分别计算的结果一定是相同的。 那么这个东西为什么很重要呢？在现在这个题里你感觉不到重要，好像觉得我还把它变麻烦了。 那我们再来看一个例子来感受一下。2减3加4，你就会发现我本来两个被人拿走三个，这个事情在我们现在是不好理解的。 我只有两个，你不能拿走我三个。但是我们可以这样想，其实就是哪些东西最后是加的呢？ 所有要加上去的东西，一开始这个二其实也是加的，相当于本来是零加了两个。最开始那个没有的就相当于是加的，所以被所有被加的东西是二和4，一共就是2加4等于6，所有要减的东西就是一个三，所以一共也就是个3。 所以最后的结果是什么呢？或者说最后的答案就拿6减3等于3就可以了。把要加的放一块，把药减的放一块儿。 如果你现在不能这样去给它调的话，你就会发现不把它合并起来，你好像就不能先算2减3了。和刚才这个题不一样，这是合并的一个思路。把加的和减的合并的一个思路。 还有一个思路就是叫做调顺序的思路，当然合并也是一种调顺序。 合并是调的比较狠，那我还有一种是怎么想呢？我2减3不能减，但是4减3能减。所以现在就是补两个，拿走三个，补四个。 那其实相当于我先补四个，再拿走三个，再补两个。反正我只要补的跟拿的中间没有发生，就是拿的跟补的那每次的量是一样的。我换一换，第一次补两个，第二次补四个和第一次补四个，第二次补两个显然没有区别。 那么2减3加4，我还可以这么想，我就把这个顺序换掉，我把它看成是先补了。比如说先补了四个再减拿掉三个再补两个，那这样的话就是一加2就等于3，这种算法在后面我们会把它叫做所谓什么带符号搬家等等。但是现在我们不用关心这个事儿，可能你们以后会听到或者已经听到了这个说法，叫带符号搬家。 这是我给你们说的第二种做法，里面很多老师会这么去讲。当然这个没问题，肯定是对的那我现在更希望大家去理解这个东西背后是干嘛，就是增增减减，调调顺序不影响就够了。好，再来看。1减2减5加4减1加2减3加5加3减4，这个看着很晕。 那我们这个时候就要合理的把它做拆分。我们来看看哪些东西是被加的，我把被加的放在一起整理一下，把被减数也放在一起。好，我们看一看哪些是被加的14253，所以被加的有这些14253。被减数是什么呢？ 好，我们再把它换一个颜色，比如说我用蓝的减2、减5、减1、减3、减4。好，所以被减数是25134。仔细看看，我加的里面是14253，其实就是123455个数字各一次。要减的25134还是123455个数字各一次。 那么好了，我要被加的部分其实是一共是什么？1加2我换顺序没关系了，3加4加5要减的一共也是1加2加3加4加5，那要加的跟要减的是一样多，或者我们从对应的角度，它是完全一一对应的。 所以它的答案就是什么？就是零，我都不需要把这个。 我都不需要把12 1加2加3加4、加五算出来，我都知道答案是0，因为它一定一样多好。 这一部分是我们通过调整顺序，或者有时候我把要加的和要减的放一块儿去思考，来做一些计算的这种变化。接着我们再来看一个例子。 这里还是加加减减，现在我们既可能要把加的减的放一块儿看，又要把这个刚才学的凑着放在一起来看。我们来看看这里，当然我可以硬算，但我现在不希望硬算，我们看看有哪些要加的，26、13、17，这是我所有要加的部分。 26加13加17，我所有要减的部分是什么？所以要减的部分是14和16，也就是一共14加16是我要减的那要减的一共是多少呢？14加16是30，这个很好算。 那我要加的部分你看看我怎么算，26加13加17 3个数相加怎么样算更快凑整，能看到哪里37对不对？所以我先算13加17，把这两个先来算，再算这个加26。那13加17就是30，再加26，这个我就很好算。 这样它等于56。好，要加的一共是56，要减的一共是30。所以原来这个东西。 这是原来这个式子，它就会等于56减30，等于26。 注意，我每次是把要加的加一块，要减的加一块儿，这个加一块的，这个加一块的，它有一个前提，它们的符号是一样的，它前面的运算符是一样的，都是减我才能加，都是加我也能加。但是我不能把这个13跟这个14加1块，因为这个13前面是加号，14这个前面是减号。 那么顺便我们说一下，第一个数前面相当于是加号。我们再强调一下，我们只有同符号的才会去相加。 这个符号就是运算符。同符号的才能加。 第一个数之前，我们是相当于是加号，第一个数前相当于加号，虽然他没有写符号。你就把它看作加号。 好再来。24加19减14，我们知道我可以硬座，但是前面一直在说有凑整，有凑整会更舒服。把加的加一起当然没问题，24加19再减14。 可是你想一想这里其实注意，24是加的，十四是减的。但是24跟14之间有一个很好的东西，什么东西呢？它们的个位是一样的，那个位一样。 24相当于我们之前说的两盒整的鸡蛋加四个，十四是一盒整的加四个，你一减出来就是十。 所以在这个时候，我们总是愿意去把它算成什么呢？就把它先算24减14，再去加19。为什么可以这么算？跟刚才一样的道理，我可以换顺序，先拿再补和先补再拿是一样的。那24减14就是10 10加19那就是29，这样我们运算非常的快。 前面我们写过这样一句话，在哪？在这儿我们说加法我怎么凑整？我是找个位上的好朋友在凑整。这里其实我也是一种凑整，但是这个凑整其实是我一个减法的凑整。大家仔细感受一下，我是24减14，造成了一个凑整。 那减法的凑整靠的是什么？不是个位上的好朋友了，靠的是什么？个位上相同，大家各位都是4，那么我一减个位就没了，就变成整十整百的数了。 所以这是我们减法凑整的一个思路。我就想要整十的运算，那我要整十的运算，加法2加8出来的13加7出来的十，只要各位是好朋友能凑出整10。那减法同样的道理，我还是要整十的运算，那我怎么做呢？ 我只要两个数的个位一样，我一减就能出现一个整十。 好，我们来看这一讲的最后一个例子，51减28加19加8。首先我们看有加有减，但是这个加的部分，如果我们像刚才一样分析，加的部分有51、有19、有8，减的部分有谁呢？有28。 你会发现同一个符号的我只能相加，这三个只能相加，这里也是相加，但是不同的之间它其实是相减的。那么这三个里面，51和19马上你就能凑出一个整。但他们都是家，并且有个好朋友在这里，但是这个减28，这里有个减8，对不对？ 有个加8，那二十八跟八之间也有个很好的东西，它们的个位一样，所以我们就可以这样子去算。我们把它写成这样，它就等于我先把51和29放在一起，这个就是70。再加上一个八，再减一个二十八，把加的全部放一块，那么这样就是70 22、80 80在我抄错了，51加19那是70。 好，现在会变这样，70加8减28，那接下去你怎么算都行了。第一个我可以把七十和八放一块儿，就是78减28先加上去。 那现在各位一样，就是50。如果你懒得这样算，你说我想先解决加8减28，这个会稍微麻烦一点点。 因为你加8减28这个事儿，对我们现在来说好像不能做，8减28你减不了，你要这么做，你还得继续把这个28给它拆掉，比如拆成一个八和一个20，那我觉得就没有必要了。因为现在这个已经很好算了，78减28他自然而然的就已经凑了出来。 好，那么这一讲的内容我们讲到这里，这一讲我们第一次体会一些巧算，不要忘记在这一讲开头的时候给大家讲到的怎么巧算，怎么提速当然重要。比如加法怎么凑整，减法怎么凑整，我怎么去调顺序等等。 把加的放一块儿，把减的放一块，但是更重要的一定是我为什么能这么算，我们可以结合各种实际的生活里的这个例子来理解加法的运算规律，当然也有减法的运算规律。好了，这一讲我们就讲到这里，我们下一讲再见。 

# 09. 数字谜基础 （二）

好，欢迎大家来到胡老师的课堂。我们这一讲的主题是数字迷基础的第二讲，我先把标题写在屏幕上，我们在之前在这一讲在前面的一讲，我们已经学习了数字谜的一些最最简单的问题。 然后在上一讲中，我们其实是又学了一些巧算的法则。那么在这一讲中，我们会把前面两讲的东西都混在了一起来看一些稍微难一点点的数字迷的这个基础的问题。 虽然难一点点，但它还是基础的问题。 好，请看第一个。现在不是要你填数字，而是要你填符号。他说在等号左边的两个数之间填加或填减使算式成立。左边有几个一，我们数一数一，234566个1，加加减减要加出一个二。 这个时候我们很多同学可能说，我试一试对吧，说不定就试出来了。还有一些同学可能试了半天也没试出结果，但我们不能把它搞成一个碰运气的一种游戏，那这种时候我们可以怎么想呢？ 你右边是2，那我先看看左边能不能先搞个二出来。如果左边也能搞个二出来，再看看剩下的能不能搞成0。 如果左边搞不出2，那么我们再想更复杂的办法。 那么右边是2，左边有六个一。你能不能先从重重几个一搞一个二呢？这个对我们来说是最最简单的事儿。 我们知道如果我一加一就是一个二，好，这里填个一就是一个二，那么后面怎么办？后面还有四个一，这里一共还有四个，你要么加要么减，但已经是二了，前面已经1加1等于二了。那我最终结果要是对的话，我只需要让这四个一的运算怎么样加加减减的运算，最后结果是变成0，加加减减变成零就可以。 我要让这几个加加减减变成零是太简单的问题了。一共四个，我们知道两个去加，两个去减，那我就能够把它变成零了。哪两个加哪两个减无所谓。比如说我们这里前两个指甲，后两个曲解，我们可以很快的算一算，1加1加1加1减1减1等于2。 可能经过这样的分析，我们还是有同学会觉得胡老师不让你讲，我自己会。但有一些更难的问题，可能你就必须去这么去尝试，尝试什么呢？能不能先凑出一个结果？先凑出右边的数。 先找几个数凑右边的数，再让剩下的想办法把它配成0，这是比较容易想到的，但它不是每次都能成功。比较简单的题可以这样做好。我们再来看在等号左边的两个数之间填加或减，使算式成立。 现在12345算下来要等于5，这个就比刚才要复杂一点了。我们还是可以用刚才的想法，我们来想一想，右边是我，左边能不能凑个五呢？那我们同学说不用凑，左边直接就有一个五，是不是？ 这里就有一个五？那只要这个五是这里是一个加号。好，那左边就是五了，左边就已经有一个五了，那剩下还有12344个数字。 我希望这四个数字怎么样加加减减，是把它凑出一个零出来。 1234怎么凑出零呢？它不一样大，那不一样大，我可以给它分组，有一些加有一些减。 但你不能把两个大的都加在一起，你把两个大的都加在一起，两个小的减的那你最后一定是不会零的，会比较大。你也更不能把这个两个小的都去加，两个大的去减。 有时候可能我要三个小的加起来正好等于一个大的那我就这三个加起来减一个。 现在这四个数字还是比较有特点的。1234我把一个小的一个大的放一起凑一凑，再把剩下的两个当中的放在这个凑一凑。那我们就会发现，一我给它加上，2和3都给它减去，一四也给它加上。那这里我只加的是哪些呢？加的就是一和4，减的是二和3。 这样一和41加是52，和31加也是5。根据我们前面学的，我们知道前面正好就是零了，再加个5，那它就等于5。 这个题我们还是用和刚才类似的办法去做的。当然我们还可以去思考，大家可以去思考一下这个题的答案是不是唯一的，也就是说你还有没有其他解，大家可以把它作为一个课后的思考。 有没有其他的填法？ 我们刚才运气很好，一眼看出了一个，再想一想有没有其他的。 好，那么这个填加减号的我们就讲到这里，接下来我们再来看一种类型的问题，我要求三角代表的数使这个等式成立，就是三角减5加20等于41，问这个三角是多少？ 那么在在前面一讲中，其实我们已经处理过了类似这样的题。 比如三角减5等于40 11，我们已经处理过了，这是我们已经会的那我们还学过了什么呢？ 我们还学过了，比如说15减5加上20这样的一种运算，我们也已经会了，我们知道怎么样去计算，怎么样去调顺序，怎么样为什么能够调顺序等等。现在其实就有点像把这两个东西结合起来。左边如果三个都给我数字我就会了，但是现在我会发现不是都给我数字。 一开始3角减五十多少我不知道，那没关系，反正你这个五是减的，这个20是加的。什么叫减5加20？ 先拿掉五个，再补上20个，那你最后起到的效果是什么？先拿掉五个再补20个，那其实就是多补了整整15个，这是从生活的实际场景来说的，我有很多很多东西，你帮我先拿掉了五个，然后再帮我补回来了20个，那其实就相当于真正帮我和一开始相比，其实是补了15个。 这个十五就是二十减5。 还有一种想法，我们说反正你是加加减减，按我们上一讲学的，我们知道，你先加个20再减个5和先减个五再加个20是没有区别的。也就是说我先拿掉五个再补回来20个，和你先给我加上20个再拿掉五个是一样的。 所以这个东西它是直接就可以先算后面的，就是三角我可以先算加上20，再减去五等于41。好，那么加20再减五这个东西，我们可以把它放到最前面，或者我们先计算这一部分。 实际上就是三角加上十五等于41。 做到这里已经变成了我们前一讲在前一讲学习过的问题。我们知道这个时候0.32元个小的部分合起来等于一个大的部分，和是大的。所以这每一部分小的三角其实就是41拆成两部分，一部分是实物，一部分是三角。三角就是41再减去15，在十五之外的这一部分于13角等于26。 如果我们做过前面的练习，我相信这个对大家很简单。那我们再来感受一个。还是求三角代表的数，使等式成立。但是现在是5减3角加13角是被减数，等于4。 那哪两个是加的呢？5和10是加的，所以我把要加的放在一起，再把这个要减的放在一起。要减的就一个，要加的就是5加10。我先算5加10放到，再拿掉这个三角等于4。好，它就是什么呢？15减3角等于4。 你看这一类问题其实就是由之前两个我们已经学习过的东西组合出来的一个新问题，之前两个都学透了，这个问题对我们来说一点都不难。好，15减3角等于45是整体，三角是一部分，四也是一部分。 十五拆成两块，一块是四，一块是三角，那这个三角是多少呢？那三角就是十五减4。 前几讲如果已经学透了的话，这里你就会觉得很轻松，我们就越讲越快了，我们在基础的地方反而会讲的慢一点。好，再来看，如果我们总结一下的话，我们可以用这样一句话叫做先算可以算的部分。 来化解，先算可以算的部分，哪些是可以算的。你5加10好算，但是你减个三角你就不好算。 那么再来现在它变成这样，10加3角减14等于25，问题的形式还是和刚才一样，要求这个三角所代表的数，10加3角减14。好，你说那可以算的部分是什么？ 可以算的部分是10减14，好像这两个数字我知道，但10减14我又不会算，小的减大的减不了，那怎么办？ 没事儿，我们还是想他的意义。你总共是加的部分是十和3角减的部分是14。那其实就是在三角这个基础上再放进来十个就叫加10，再拿掉14。 那你放进来十个再拿掉14个，那拿掉14个是不是相当于先拿掉十个再拿掉四个？所以我第一种理解，我可以把它想成这样，就是十我把三角写到前面，其实就是三角加10减14等于25。那这个10减14我不能减，但是减14是什么意思？ 不就是拿掉十个再拿掉四个，所以就先拿掉十个再拿掉四个，它等于25。 或者你直接想50个拿走14个，它相当于拿走了四个也可以，随便用哪个角度都可以讲明白，所以它就是三角减4等于25，三角是整体，4和25是部分。所以三角等于25，加上四三角就等于。 29 3角等于29。那么这里我们举了三个看起来不大一样的例子，但是它的大的思路是一致的，只不过在细节处理上有所不同。 这一部分我们先学到这里，接下去我们再来看一些数字谜里面的比大小的问题。 现在说有一个三角加上一个七，等于一个圆圈减去一个三。三角和圆圈谁对应的这个数更大？ 三角加了七个，我补了七个，相当于圆圈里拿掉了三个。你可以想象从这个三角里面加进去了7个1234567，本来有这么多，你加了七个进去，人家是本来有这么多，本来有很多很多，你还把它拿掉了三个，现在你才两边才一样多，我加上一部分和你减去一些一样多，我变大了，我加了以后是不是变大了？ 圆圈减3以后是不是变小了，我变大一点跟你变小1.1样多。那我原来可是比我变大之前更小，你原来可是比你减小之前更大我变大一点和你减小1.1样多，那我本来肯定要比你更小。 我们可以把它想象成一个胖子一个瘦子是吧？一个胖子重了七斤，一个瘦子重了七斤，我增肥了七斤，你减肥了三斤，我才跟你一样重。 那在我增肥之前，你减肥之前，我肯定比你瘦，这是第一种理解。那么还有一种理解是什么呢？ 还有一种理解我们也可以这样想，我也可以想成这样，三角加上7，现在是等于圆圈减3，我也不知道这个是多少。 我就假设三角加7以后，它就正好又等于一个东西。比如等于一个五角星，那圆圈减三也等于一个五角星。好，那么这样的话我把它写成两个，大家看着更舒服一点。 因为如果三角加七等于五角星，那么圆圈减三也等于五角星。因为它们是一样的，那么三角加七等于五角星，那三角是什么？就是一个五角星减7，那圆圈是什么？ 圆圈是五角星加3。 那你说三角跟圆圈谁大？一五角星代表同样一个数，你一个是它减了7，一个是它加了3。那当然三角要比圆圈小。 同样的道理我们还可以来感受，现在是一加一减，我们再来感受同加或者同减的情况。三角加上七等于圆圈加上3。那么像刚才一样，我第一种我可以直接理解，我加了七个，你加了三个，我变胖了七斤，你变胖了三斤。 那你说谁更重？谁原来更多？我补了，可是七，我补的多，你补的少。我为什么要补的多？因为我本来小，所以我要补的更多一点。如果我本来就很多了，我只要补一点点就够了，所以我补的多的应该是原来小的。补的多的。 原来小我补了7的比补了三的要小。那么也可以像刚才一样，我们也可以这样做，三角加七我把它看成是一个五角星，圆圈加三我不知道是多少，那我也就可以用一个五角星来代表一下。你想圆圈是啊三角是什么？三角就是五角星减7。那圆圈是什么？ 圆圈就是五角星减3。那减掉7肯定变得更小，比减掉三更小。什么叫减7？倒着数了七个。什么叫减三倒着数了三格，是不是？ 假设五角星在这里，那三角就是一格、两格、三格、四格、五格，前面还要画六格七格，这就是三角的位置。那圆圈在哪？往回三格，123，那圆圈就在这儿，所以三角就比圆圈要小。 好，那么同样的，如果3角减7等于圆圈减3，我们现在就越来越快了。我减掉了7，从我这儿拿掉了七个和你这儿拿掉了三个是一样多的。我拿掉了这么多才跟你拿掉三个一样多。那我本来很多，所以我三角就是大于圆圈。 刚才我们说的叫做补的多的，原来小，现在大家都拿掉了一点。你都拿掉了一点以后。那我拿的多的应该是原来大，也就去掉多的。原来是更大的。 也可以向前面那个思路来理解，三角减7。假设它就是一个五角星，圆圈减三这个数也是五角星。 因为它们相等，那么三角就是五角星加7，圆圈就是五角星加3。那显然毫无疑问，五角星加7肯定比五角星加三要大，所以三角就比比圆圈大。 这三个我们都可以直接理解，或者你这两个相等，我都让你等于一个五角星来做处理。 好，那么再来变得更麻烦一点，三角加五角星等于20，圆圈加五角星等于12。这怎么办？一下有三个字母搞得很麻烦，没关系，它还是整体和部分的问题。 三角加五角星也就是从三角加了这么多东西是20，圆圈加了这么多东西是十二。注意，我们加的是一样多的。那加了一样多以后，我20是比十二大的。 三角夹着点比圆圈加值点要大，那就说明三角应该比圆圈大。 这就好比假设你现在随便说说，假设你现在九岁或者八岁你哥哥现在比如说。十岁，比如说你现在八岁，你哥哥十岁。那五年以后就是现在五年以后你变成了13岁，8加5，那你哥哥变成了15岁，这个五角星就相当于这个五年。 我们大家过了五年，大家过了五年以后你会发现你哥哥比你大，那现在其实你哥哥也比你大，因为我们加的一样多，我们加的一样多以后我比你大，那我原来也比你大，这个就是什么？它背后其实就有对应思想。 你看我这里，比如说是这里六个，你这里是三个，然后大家补上一样多，我这里也补三个，补一样多。那补一样多以后，我比较大小还是可以像原来一样比原来怎么比原来比较大小。 我们说有一种笔法就叫做这个一一连线，对不对？看谁最后多出来，那你补上了三个，其实你会在连线的过程中大家一起消掉，你补的这三个其实是不影响你这个连线最后谁多的。 所以两个数比大小和它加上同一个数以后，比大小结果是一样的，不改变。所以这个事情我们叫做两个数，我把这个结论写一下，都加上。 同一个数。不改变原来的大小关系，原来我比你大，大家都加个我还是比你大。原来我比你小，大家都加个我还是比你小。如果放在我们刚才举的例子里，就是你哥哥总归是你哥哥，过五年他还是你哥哥。因为大家都加了一个五，那么加是这样，其实减也是这样。 大家可以想一想，我们再来看一个例子，三角减五角星是20，圆圈减五角星是十二，三角和圆圈谁大？三角减五角星是20，圆圈减五角星是十二，20比12大。那我从三角里和圆圈里减掉了同时减掉了这么多。什么叫同时减掉？ 如果从图形的角度，从数数的角度我们都可以理解，从图形的角度就是大家都往前假设，这个是五角星。 大家都减掉，不是五角星，这个是圆圈。 大家都减个一，大家都减个二，大家都减个三，或者是大家都减个二，那就是大家一起往回走两格。 那大家一起往回走两格以后，我比你大，我现在还是比你大。 现在它是大家一起往回走了，20个297一起往回走了，五角星往外走，五角星格这个市集我不知道，但是没有影响，反正大家剪了一样多以后，我比你多，我比你大，那我本来也比你大，所以这件事情跟刚才几乎是完全一样。刚才你看大家加一个五角星，20比12大，所以三角比圆圈大。现在就是大家减一个五角星，20比12代，所以我还是有三角比圆圈大。 刚才我写的一句话叫做两个数都加上同一个数，不改变原来的大小关系。那现在就是什么两个数都大家能自己写出来了吗？ 减去同一个数不改变原来的大小关系，拿具体的数来说，比如说十比八大，那你说10减1和八减一谁大？10减2和8减2谁大我本来比你多，我有十个鸡蛋，你有八个鸡蛋，大家拿掉一个鸡蛋我还是比你多，大家拿掉两个鸡蛋我也比你多。 我跟根本不用算出来这个数字是多少，我就能做大小的比较。好了，数字谜的基础的第二讲我们就讲到这里了，我们下一讲再见。 