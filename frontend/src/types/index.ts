// API响应格式
export interface APIResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
    timestamp: string;
    requestId: string;
  };
  errors?: ErrorDetail[];
}

export interface ErrorDetail {
  field?: string;
  code: string;
  message: string;
}

// 对应计数相关类型
export type CorrespondenceScenario = 'shepherd-sheep' | 'rope-knot' | 'connect-master' | 'custom';
export type CorrespondenceSubType = 'one-to-one-mapping' | 'counting-comparison' | 'visual-correspondence';
export type ConnectionType = 'line' | 'drag-drop' | 'gesture';
export type ItemType = 'sheep' | 'stone' | 'knot' | 'apple' | 'box' | 'custom';
export type ComparisonResult = 'equal' | 'left-more' | 'right-more';
export type UnderstandingLevel = 'excellent' | 'good' | 'needs-practice';

// 对应项目接口
export interface CorrespondenceItem {
  id: string;
  type: ItemType;
  displayName: string;
  imageUrl: string;
  audioUrl?: string;
  position: {
    x: number;
    y: number;
  };
  isConnectable: boolean;
  maxConnections: number;
}

// 对应映射关系
export interface CorrespondenceMapping {
  leftItemId: string;
  rightItemId: string;
  isCorrect: boolean;
  connectionType: ConnectionType;
}

// 练习内容
export interface CorrespondenceExercise {
  id: string;
  chapterId: number;
  type: 'correspondence';
  subType: CorrespondenceSubType;
  difficulty: 1 | 2 | 3 | 4 | 5;
  title: string;
  description: string;
  content: {
    scenario: CorrespondenceScenario;
    leftItems: CorrespondenceItem[];
    rightItems: CorrespondenceItem[];
    expectedMappings: CorrespondenceMapping[];
    allowPartialMapping: boolean;
    showResult: 'missing' | 'exact' | 'extra' | 'auto-detect';
  };
  correctAnswer: {
    mappings: CorrespondenceMapping[];
    comparison: ComparisonResult;
    count: {
      left: number;
      right: number;
      difference: number;
    };
  };
  hints: string[];
  explanation: string;
  relatedConcepts: string[];
  audioInstructions?: string;
  visualEffects: {
    successAnimation: string;
    errorFeedback: string;
    completionCelebration: string;
  };
}

// 游戏会话数据
export interface CorrespondenceGameSession {
  id: string;
  studentId: string;
  exerciseId: string;
  gameType: 'correspondence-counting';
  scenario: string;
  startTime: string;
  endTime?: string;
  isCompleted: boolean;
  score: number;
  userMappings: CorrespondenceMapping[];
  attemptCount: number;
  mistakeDetails: {
    incorrectMappings: CorrespondenceMapping[];
    missedMappings: CorrespondenceMapping[];
    extraMappings: CorrespondenceMapping[];
  };
  timePerMapping: number[];
  hintUsed: boolean;
  finalResult: {
    accuracy: number;
    completionTime: number;
    understanding: UnderstandingLevel;
  };
}

// API请求类型
export interface CreateExerciseRequest {
  chapterId: number;
  subType: CorrespondenceSubType;
  difficulty: 1 | 2 | 3 | 4 | 5;
  title: string;
  description: string;
  scenario: CorrespondenceScenario;
  leftItemCount: number;
  rightItemCount: number;
  customItems?: {
    leftType?: ItemType;
    rightType?: ItemType;
  };
}

export interface StartGameRequest {
  studentId: string;
  exerciseId: string;
}

export interface SubmitMappingRequest {
  mappings: Omit<CorrespondenceMapping, 'isCorrect'>[];
}

// 学习历史和分析
export interface StudentHistory {
  exercises: CorrespondenceGameSession[];
  totalCompleted: number;
  averageAccuracy: number;
  totalTimeSpent: number;
  strongAreas: string[];
  weakAreas: string[];
}

export interface AnalyticsData {
  studentId?: string;
  timeRange: {
    start: string;
    end: string;
  };
  summary: {
    totalSessions: number;
    averageAccuracy: number;
    averageCompletionTime: number;
    mostDifficultConcepts: string[];
    improvementTrends: Array<{
      date: string;
      accuracy: number;
      completionTime: number;
    }>;
  };
}

// UI相关类型
export interface GameUIState {
  currentExercise: CorrespondenceExercise | null;
  currentSession: CorrespondenceGameSession | null;
  userMappings: CorrespondenceMapping[];
  isSubmitting: boolean;
  showHints: boolean;
  showResult: boolean;
  feedbackMessage: string;
  celebrationActive: boolean;
}

export interface ConnectionLine {
  id: string;
  fromItemId: string;
  toItemId: string;
  startPosition: { x: number; y: number };
  endPosition: { x: number; y: number };
  isCorrect?: boolean;
  animating?: boolean;
}