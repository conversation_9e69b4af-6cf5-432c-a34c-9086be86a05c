import type { 
  CorrespondenceExercise, 
  CorrespondenceGameSession, 
  APIResponse 
} from '../types';

// 模拟练习数据
export const createMockExercise = (scenario: string): CorrespondenceExercise => {
  const exerciseId = `mock_${scenario}_${Date.now()}`;
  
  // 生成左右物品
  const leftItems = Array.from({ length: 4 + Math.floor(Math.random() * 3) }, (_, i) => ({
    id: `left_${i}`,
    type: scenario === 'shepherd-sheep' ? 'sheep' as const : 'custom' as const,
    displayName: scenario === 'shepherd-sheep' ? `羊${i + 1}` : `物品${i + 1}`,
    imageUrl: '',
    position: { x: 0, y: 0 },
    isConnectable: true,
    maxConnections: 1
  }));

  const rightItems = Array.from({ length: leftItems.length }, (_, i) => ({
    id: `right_${i}`,
    type: scenario === 'shepherd-sheep' ? 'stone' as const : 'custom' as const,
    displayName: scenario === 'shepherd-sheep' ? `石子${i + 1}` : `容器${i + 1}`,
    imageUrl: '',
    position: { x: 0, y: 0 },
    isConnectable: true,
    maxConnections: 1
  }));

  // 生成期望的映射关系
  const expectedMappings = leftItems.map((leftItem, i) => ({
    leftItemId: leftItem.id,
    rightItemId: rightItems[i]?.id || '',
    isCorrect: true,
    connectionType: 'line' as const
  })).filter(mapping => mapping.rightItemId);

  return {
    id: exerciseId,
    chapterId: 1,
    type: 'correspondence',
    subType: 'one-to-one-mapping',
    difficulty: 1,
    title: `${scenario}练习`,
    description: '学习一一对应概念的游戏',
    content: {
      scenario: scenario as any,
      leftItems,
      rightItems,
      expectedMappings,
      allowPartialMapping: false,
      showResult: 'auto-detect'
    },
    correctAnswer: {
      mappings: expectedMappings,
      comparison: leftItems.length === rightItems.length ? 'equal' : 
                  leftItems.length > rightItems.length ? 'left-more' : 'right-more',
      count: {
        left: leftItems.length,
        right: rightItems.length,
        difference: Math.abs(leftItems.length - rightItems.length)
      }
    },
    hints: [
      '观察每个物品，找到它们之间的对应关系',
      '试着一对一地连接左边和右边的物品',
      '注意数量是否相等'
    ],
    explanation: '一一对应是数学的基础概念，通过这个练习你可以理解每个物品都有唯一的配对关系。',
    relatedConcepts: ['一一对应', '数量比较', '集合配对'],
    visualEffects: {
      successAnimation: 'sparkle',
      errorFeedback: 'shake',
      completionCelebration: 'confetti'
    }
  };
};

// 模拟游戏会话数据
export const createMockGameSession = (
  exerciseId: string, 
  studentId: string
): CorrespondenceGameSession => ({
  id: `session_${Date.now()}`,
  studentId,
  exerciseId,
  gameType: 'correspondence-counting',
  scenario: 'shepherd-sheep',
  startTime: new Date().toISOString(),
  isCompleted: false,
  score: 0,
  userMappings: [],
  attemptCount: 1,
  mistakeDetails: {
    incorrectMappings: [],
    missedMappings: [],
    extraMappings: []
  },
  timePerMapping: [],
  hintUsed: false,
  finalResult: {
    accuracy: 0,
    completionTime: 0,
    understanding: 'needs-practice'
  }
});

// 模拟API响应包装器
export const createMockAPIResponse = <T>(data: T): APIResponse<T> => ({
  success: true,
  code: 200,
  message: '操作成功',
  data,
  meta: {
    timestamp: new Date().toISOString(),
    requestId: `req_${Date.now()}`
  }
});

// 评估用户答案的模拟函数
export const evaluateUserMappings = (
  exercise: CorrespondenceExercise,
  userMappings: any[],
  session: CorrespondenceGameSession
): CorrespondenceGameSession => {
  const correctMappings = exercise.correctAnswer.mappings;
  let score = 0;
  let correctCount = 0;

  // 检查每个用户映射
  const evaluatedMappings = userMappings.map(userMapping => {
    const isCorrect = correctMappings.some(correct => 
      correct.leftItemId === userMapping.leftItemId && 
      correct.rightItemId === userMapping.rightItemId
    );
    
    if (isCorrect) {
      correctCount++;
      score += 10;
    }
    
    return {
      ...userMapping,
      isCorrect
    };
  });

  // 计算错误分析
  const incorrectMappings = evaluatedMappings.filter(m => !m.isCorrect);
  const missedMappings = correctMappings.filter(correct => 
    !userMappings.some(user => 
      user.leftItemId === correct.leftItemId && 
      user.rightItemId === correct.rightItemId
    )
  );

  const accuracy = correctMappings.length > 0 ? 
    (correctCount / correctMappings.length) * 100 : 0;
    
  const understanding = accuracy >= 90 ? 'excellent' : 
                       accuracy >= 70 ? 'good' : 'needs-practice';

  const completionTime = Math.floor(
    (Date.now() - new Date(session.startTime).getTime()) / 1000
  );

  return {
    ...session,
    userMappings: evaluatedMappings,
    isCompleted: true,
    score,
    attemptCount: session.attemptCount + 1,
    endTime: new Date().toISOString(),
    mistakeDetails: {
      incorrectMappings,
      missedMappings,
      extraMappings: []
    },
    finalResult: {
      accuracy,
      completionTime,
      understanding
    }
  };
};