import axios from 'axios';
import type {
  APIResponse,
  CorrespondenceExercise,
  CorrespondenceGameSession,
  CreateExerciseRequest,
  StartGameRequest,
  SubmitMappingRequest,
  StudentHistory,
  AnalyticsData
} from '../types';
import { 
  createMockExercise, 
  createMockGameSession, 
  createMockAPIResponse, 
  evaluateUserMappings 
} from './mockData';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || true; // 默认使用模拟数据

// 存储当前的练习和会话数据
let currentExercise: CorrespondenceExercise | null = null;
let currentSession: CorrespondenceGameSession | null = null;

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 响应拦截器 - 统一处理API响应格式
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.data) {
      throw error.response.data;
    }
    throw {
      success: false,
      code: error.response?.status || 500,
      message: error.message || '网络请求失败',
      errors: []
    };
  }
);

/**
 * 对应计数模块API客户端
 */
export class CorrespondenceAPI {
  
  /**
   * 创建练习
   */
  static async createExercise(data: CreateExerciseRequest): Promise<APIResponse<CorrespondenceExercise>> {
    if (USE_MOCK_DATA) {
      // 使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟
      currentExercise = createMockExercise(data.scenario);
      return createMockAPIResponse(currentExercise);
    }
    return apiClient.post('/correspondence/exercises', data);
  }

  /**
   * 获取练习列表
   */
  static async getExercises(params?: {
    chapterId?: number;
    difficulty?: number;
  }): Promise<APIResponse<CorrespondenceExercise[]>> {
    return apiClient.get('/correspondence/exercises', { params });
  }

  /**
   * 获取单个练习详情
   */
  static async getExercise(exerciseId: string): Promise<APIResponse<CorrespondenceExercise>> {
    return apiClient.get(`/correspondence/exercises/${exerciseId}`);
  }

  /**
   * 开始游戏会话
   */
  static async startGame(data: StartGameRequest): Promise<APIResponse<CorrespondenceGameSession>> {
    if (USE_MOCK_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));
      if (!currentExercise) {
        throw new Error('请先创建练习');
      }
      currentSession = createMockGameSession(data.exerciseId, data.studentId);
      return createMockAPIResponse(currentSession);
    }
    return apiClient.post('/correspondence/games/start', data);
  }

  /**
   * 提交映射答案
   */
  static async submitMapping(
    sessionId: string, 
    data: SubmitMappingRequest
  ): Promise<APIResponse<CorrespondenceGameSession>> {
    if (USE_MOCK_DATA) {
      await new Promise(resolve => setTimeout(resolve, 800)); // 模拟评估时间
      if (!currentExercise || !currentSession) {
        throw new Error('游戏会话不存在');
      }
      currentSession = evaluateUserMappings(currentExercise, data.mappings, currentSession);
      return createMockAPIResponse(currentSession);
    }
    return apiClient.post(`/correspondence/games/${sessionId}/submit`, data);
  }

  /**
   * 获取游戏会话详情
   */
  static async getGameSession(sessionId: string): Promise<APIResponse<CorrespondenceGameSession>> {
    return apiClient.get(`/correspondence/games/${sessionId}`);
  }

  /**
   * 获取学生练习历史
   */
  static async getStudentHistory(
    studentId: string, 
    params?: { chapterId?: number }
  ): Promise<APIResponse<StudentHistory>> {
    return apiClient.get(`/correspondence/students/${studentId}/history`, { params });
  }

  /**
   * 获取学习分析数据
   */
  static async getAnalytics(params?: {
    studentId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<APIResponse<AnalyticsData>> {
    return apiClient.get('/correspondence/analytics', { params });
  }
}

/**
 * 工具函数：生成临时学生ID
 * 在实际应用中应该从用户认证系统获取
 */
export const generateTempStudentId = (): string => {
  const stored = localStorage.getItem('temp_student_id');
  if (stored) {
    return stored;
  }
  
  const newId = `temp_student_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  localStorage.setItem('temp_student_id', newId);
  return newId;
};

/**
 * 工具函数：API错误处理
 */
export const handleAPIError = (error: any): string => {
  if (error.errors && error.errors.length > 0) {
    return error.errors.map((e: any) => e.message).join(', ');
  }
  return error.message || '操作失败，请稍后重试';
};

export default CorrespondenceAPI;