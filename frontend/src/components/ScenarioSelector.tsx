import type { CorrespondenceScenario } from '../types';

interface ScenarioSelectorProps {
  onScenarioSelect: (scenario: CorrespondenceScenario) => void;
}

const ScenarioSelector: React.FC<ScenarioSelectorProps> = ({ onScenarioSelect }) => {
  const scenarios = [
    {
      id: 'shepherd-sheep' as CorrespondenceScenario,
      title: '牧羊人数羊',
      description: '帮助牧羊人为每只羊找到对应的小石子，学习一一对应的概念',
      icon: '🐑',
      difficulty: '简单',
      color: '#10b981'
    },
    {
      id: 'rope-knot' as CorrespondenceScenario,
      title: '结绳计数',
      description: '用绳子上的结来记录物品数量，体验古代计数方法',
      icon: '🪢',
      difficulty: '中等',
      color: '#f59e0b'
    },
    {
      id: 'connect-master' as CorrespondenceScenario,
      title: '连线大师',
      description: '连接相关联的物品，训练逻辑思维和配对能力',
      icon: '🔗',
      difficulty: '困难',
      color: '#ef4444'
    }
  ];

  return (
    <div className="scenario-selector">
      <h2 style={{ color: '#0369a1', marginBottom: '1rem' }}>选择学习场景</h2>
      <p style={{ color: '#64748b', marginBottom: '2rem', fontSize: '1.1rem' }}>
        每个场景都有不同的难度和学习重点，选择适合你的挑战！
      </p>
      
      <div className="scenario-grid">
        {scenarios.map((scenario) => (
          <div
            key={scenario.id}
            className="scenario-card"
            onClick={() => onScenarioSelect(scenario.id)}
            style={{ borderLeftColor: scenario.color, borderLeftWidth: '4px' }}
          >
            <div className="scenario-icon">{scenario.icon}</div>
            <h3>{scenario.title}</h3>
            <p>{scenario.description}</p>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '1rem'
            }}>
              <span style={{
                background: scenario.color,
                color: 'white',
                padding: '0.25rem 0.75rem',
                borderRadius: '12px',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}>
                {scenario.difficulty}
              </span>
              <span style={{
                color: scenario.color,
                fontWeight: '600',
                fontSize: '1rem'
              }}>
                开始游戏 →
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScenarioSelector;