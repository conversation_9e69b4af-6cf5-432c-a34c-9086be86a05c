import React from 'react';
import type { CorrespondenceGameSession } from '../../types';

interface ScorePanelProps {
  session: CorrespondenceGameSession;
  mappingCount: number;
  expectedCount: number;
}

const ScorePanel: React.FC<ScorePanelProps> = ({ 
  session, 
  mappingCount, 
  expectedCount 
}) => {
  // 计算进度百分比
  const progress = expectedCount > 0 ? (mappingCount / expectedCount) * 100 : 0;
  
  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 计算已用时间
  const getElapsedTime = (): number => {
    const startTime = new Date(session.startTime).getTime();
    const currentTime = session.endTime ? new Date(session.endTime).getTime() : Date.now();
    return Math.floor((currentTime - startTime) / 1000);
  };

  const elapsedTime = getElapsedTime();

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      background: 'white',
      padding: '1rem 1.5rem',
      borderRadius: '12px',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      marginBottom: '1.5rem',
      border: '1px solid #e5e7eb'
    }}>
      {/* 进度信息 */}
      <div style={{ flex: 1 }}>
        <div style={{
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '0.5rem'
        }}>
          连接进度
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem'
        }}>
          <div style={{
            flex: 1,
            height: '8px',
            background: '#e5e7eb',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{
              height: '100%',
              background: progress === 100 ? '#10b981' : '#3b82f6',
              width: `${Math.min(progress, 100)}%`,
              transition: 'width 0.3s ease',
              borderRadius: '4px'
            }} />
          </div>
          <span style={{
            fontSize: '0.875rem',
            fontWeight: '600',
            color: progress === 100 ? '#10b981' : '#3b82f6'
          }}>
            {mappingCount}/{expectedCount}
          </span>
        </div>
      </div>

      {/* 分数 */}
      <div style={{
        textAlign: 'center',
        margin: '0 2rem'
      }}>
        <div style={{
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '0.25rem'
        }}>
          当前分数
        </div>
        <div style={{
          fontSize: '1.5rem',
          fontWeight: '700',
          color: '#0369a1'
        }}>
          {session.score}
        </div>
      </div>

      {/* 尝试次数 */}
      <div style={{
        textAlign: 'center',
        margin: '0 2rem'
      }}>
        <div style={{
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '0.25rem'
        }}>
          尝试次数
        </div>
        <div style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: session.attemptCount > 3 ? '#ef4444' : session.attemptCount > 1 ? '#f59e0b' : '#10b981'
        }}>
          {session.attemptCount || 1}
        </div>
      </div>

      {/* 用时 */}
      <div style={{
        textAlign: 'center'
      }}>
        <div style={{
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '0.25rem'
        }}>
          用时
        </div>
        <div style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: '#6b7280',
          fontFamily: 'monospace'
        }}>
          {formatTime(elapsedTime)}
        </div>
      </div>

      {/* 状态指示器 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        marginLeft: '2rem'
      }}>
        <div style={{
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          background: session.isCompleted ? '#10b981' : mappingCount > 0 ? '#f59e0b' : '#6b7280'
        }} />
        <span style={{
          fontSize: '0.875rem',
          color: '#64748b'
        }}>
          {session.isCompleted ? '已完成' : mappingCount > 0 ? '进行中' : '未开始'}
        </span>
      </div>
    </div>
  );
};

export default ScorePanel;