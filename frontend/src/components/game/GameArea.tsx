import React, { useState, useRef } from 'react';
import type { CorrespondenceExercise, CorrespondenceMapping, CorrespondenceScenario } from '../../types';
import './GameArea.css';

interface GameAreaProps {
  exercise: CorrespondenceExercise;
  userMappings: CorrespondenceMapping[];
  onConnection: (leftItemId: string, rightItemId: string) => void;
  scenario: CorrespondenceScenario;
}

interface DragState {
  isDragging: boolean;
  startItemId: string | null;
  startPosition: { x: number; y: number } | null;
  currentPosition: { x: number; y: number } | null;
}

const GameArea: React.FC<GameAreaProps> = ({ 
  exercise, 
  userMappings, 
  onConnection, 
  scenario 
}) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    startItemId: null,
    startPosition: null,
    currentPosition: null
  });
  
  const gameAreaRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  // 获取物品元素的中心位置
  const getItemCenterPosition = (itemId: string): { x: number; y: number } | null => {
    const element = document.getElementById(`item-${itemId}`);
    if (!element || !gameAreaRef.current) return null;

    const gameAreaRect = gameAreaRef.current.getBoundingClientRect();
    const itemRect = element.getBoundingClientRect();

    return {
      x: itemRect.left + itemRect.width / 2 - gameAreaRect.left,
      y: itemRect.top + itemRect.height / 2 - gameAreaRect.top
    };
  };

  // 开始拖拽
  const handleMouseDown = (itemId: string, isLeftSide: boolean) => {
    if (!isLeftSide) return; // 只允许从左侧开始拖拽

    const position = getItemCenterPosition(itemId);
    if (!position) return;

    setDragState({
      isDragging: true,
      startItemId: itemId,
      startPosition: position,
      currentPosition: position
    });
  };

  // 拖拽中
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!dragState.isDragging || !gameAreaRef.current) return;

    const gameAreaRect = gameAreaRef.current.getBoundingClientRect();
    const currentPosition = {
      x: e.clientX - gameAreaRect.left,
      y: e.clientY - gameAreaRect.top
    };

    setDragState(prev => ({
      ...prev,
      currentPosition
    }));
  };

  // 结束拖拽
  const handleMouseUp = (targetItemId?: string) => {
    if (!dragState.isDragging || !dragState.startItemId) {
      setDragState({
        isDragging: false,
        startItemId: null,
        startPosition: null,
        currentPosition: null
      });
      return;
    }

    if (targetItemId && targetItemId !== dragState.startItemId) {
      // 检查目标是否在右侧
      const targetIsRight = exercise.content.rightItems.some(item => item.id === targetItemId);
      if (targetIsRight) {
        onConnection(dragState.startItemId, targetItemId);
      }
    }

    setDragState({
      isDragging: false,
      startItemId: null,
      startPosition: null,
      currentPosition: null
    });
  };

  // 获取场景特定的样式和图标
  const getScenarioConfig = () => {
    switch (scenario) {
      case 'shepherd-sheep':
        return {
          leftIcon: '🐑',
          rightIcon: '🪨',
          leftLabel: '羊群',
          rightLabel: '石子',
          backgroundColor: '#fef3c7'
        };
      case 'rope-knot':
        return {
          leftIcon: '📦',
          rightIcon: '🪢',
          leftLabel: '物品',
          rightLabel: '绳结',
          backgroundColor: '#fef3c7'
        };
      case 'connect-master':
        return {
          leftIcon: '🍎',
          rightIcon: '📦',
          leftLabel: '物品',
          rightLabel: '容器',
          backgroundColor: '#fef3c7'
        };
      default:
        return {
          leftIcon: '●',
          rightIcon: '○',
          leftLabel: '左侧',
          rightLabel: '右侧',
          backgroundColor: '#f3f4f6'
        };
    }
  };

  const config = getScenarioConfig();

  // 渲染连线
  const renderConnections = () => {
    return userMappings.map((mapping, index) => {
      const startPos = getItemCenterPosition(mapping.leftItemId);
      const endPos = getItemCenterPosition(mapping.rightItemId);
      
      if (!startPos || !endPos) return null;

      return (
        <line
          key={`connection-${index}`}
          x1={startPos.x}
          y1={startPos.y}
          x2={endPos.x}
          y2={endPos.y}
          stroke="#10b981"
          strokeWidth="3"
          strokeDasharray="none"
        />
      );
    });
  };

  // 渲染正在拖拽的线
  const renderDragLine = () => {
    if (!dragState.isDragging || !dragState.startPosition || !dragState.currentPosition) {
      return null;
    }

    return (
      <line
        x1={dragState.startPosition.x}
        y1={dragState.startPosition.y}
        x2={dragState.currentPosition.x}
        y2={dragState.currentPosition.y}
        stroke="#6b7280"
        strokeWidth="2"
        strokeDasharray="5,5"
      />
    );
  };

  return (
    <div 
      className="game-area-container" 
      ref={gameAreaRef}
      onMouseMove={handleMouseMove}
      onMouseUp={() => handleMouseUp()}
      onMouseLeave={() => handleMouseUp()}
    >
      {/* SVG 连线层 */}
      <svg 
        ref={svgRef}
        className="connection-svg"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}
      >
        {renderConnections()}
        {renderDragLine()}
      </svg>

      {/* 左侧物品区域 */}
      <div className="items-container left-items">
        <h3 className="items-title">
          {config.leftIcon} {config.leftLabel}
        </h3>
        <div className="items-grid">
          {exercise.content.leftItems.map((item) => {
            const isConnected = userMappings.some(m => m.leftItemId === item.id);
            const isDragStart = dragState.startItemId === item.id;
            
            return (
              <div
                key={item.id}
                id={`item-${item.id}`}
                className={`game-item left-item ${isConnected ? 'connected' : ''} ${isDragStart ? 'dragging' : ''}`}
                onMouseDown={() => handleMouseDown(item.id, true)}
                style={{ cursor: 'grab' }}
              >
                <div className="item-icon">
                  {config.leftIcon}
                </div>
                <div className="item-label">
                  {item.displayName}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 右侧物品区域 */}
      <div className="items-container right-items">
        <h3 className="items-title">
          {config.rightIcon} {config.rightLabel}
        </h3>
        <div className="items-grid">
          {exercise.content.rightItems.map((item) => {
            const isConnected = userMappings.some(m => m.rightItemId === item.id);
            
            return (
              <div
                key={item.id}
                id={`item-${item.id}`}
                className={`game-item right-item ${isConnected ? 'connected' : ''}`}
                onMouseUp={() => handleMouseUp(item.id)}
                style={{ cursor: 'crosshair' }}
              >
                <div className="item-icon">
                  {config.rightIcon}
                </div>
                <div className="item-label">
                  {item.displayName}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 连接计数显示 */}
      <div className="connection-counter">
        已连接: {userMappings.length} / {Math.min(exercise.content.leftItems.length, exercise.content.rightItems.length)}
      </div>
    </div>
  );
};

export default GameArea;