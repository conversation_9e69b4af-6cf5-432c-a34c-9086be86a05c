/* 游戏区域容器 */
.game-area-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 400px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 2rem;
  overflow: hidden;
}

/* 物品容器 */
.items-container {
  flex: 1;
  max-width: 45%;
  z-index: 2;
}

.items-title {
  text-align: center;
  color: #0369a1;
  font-size: 1.5rem;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-items: center;
}

/* 游戏物品样式 */
.game-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: white;
  border: 3px solid transparent;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  user-select: none;
}

.game-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.left-item {
  border-color: #fbbf24;
}

.left-item:hover {
  border-color: #f59e0b;
  background: #fef3c7;
}

.left-item.dragging {
  border-color: #f59e0b;
  background: #fef3c7;
  transform: scale(1.05);
  z-index: 10;
}

.right-item {
  border-color: #10b981;
}

.right-item:hover {
  border-color: #059669;
  background: #d1fae5;
}

.game-item.connected {
  border-color: #10b981;
  background: #d1fae5;
}

.game-item.connected .item-icon {
  filter: brightness(1.1);
}

/* 物品图标和标签 */
.item-icon {
  font-size: 2rem;
  margin-bottom: 0.25rem;
  line-height: 1;
}

.item-label {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
  font-weight: 500;
}

/* 连接计数器 */
.connection-counter {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #0369a1;
  font-weight: 600;
  z-index: 5;
}

/* SVG 连线样式 */
.connection-svg line {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-area-container {
    flex-direction: column;
    gap: 2rem;
    padding: 1rem;
    min-height: 500px;
  }

  .items-container {
    max-width: 100%;
    width: 100%;
  }

  .items-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 0.75rem;
  }

  .game-item {
    width: 70px;
    height: 70px;
  }

  .item-icon {
    font-size: 1.5rem;
  }

  .item-label {
    font-size: 0.7rem;
  }

  .items-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .connection-counter {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    margin: 1rem auto;
    display: inline-block;
  }
}

/* 拖拽状态下的视觉反馈 */
.game-area-container.dragging {
  cursor: grabbing;
}

.game-area-container.dragging .right-item {
  border-color: #6b7280;
  opacity: 0.8;
}

.game-area-container.dragging .right-item:hover {
  border-color: #10b981;
  background: #d1fae5;
  opacity: 1;
  transform: scale(1.05);
}

/* 动画效果 */
@keyframes connection-success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.game-item.connection-success {
  animation: connection-success 0.3s ease;
}

/* 连线成功的视觉反馈 */
.connection-svg line.success {
  stroke: #10b981;
  stroke-width: 4;
  animation: line-success 0.5s ease;
}

@keyframes line-success {
  0% {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
  }
}