import React from 'react';
import type { CorrespondenceGameSession, CorrespondenceExercise } from '../../types';

interface ResultModalProps {
  result: CorrespondenceGameSession;
  exercise: CorrespondenceExercise;
  onClose: () => void;
  onRestart: () => void;
}

const ResultModal: React.FC<ResultModalProps> = ({ 
  result, 
  exercise, 
  onClose, 
  onRestart 
}) => {
  // 计算准确率
  const accuracy = result.finalResult?.accuracy || 0;
  const understanding = result.finalResult?.understanding || 'needs-practice';
  
  // 获取理解水平的显示信息
  const getUnderstandingInfo = () => {
    switch (understanding) {
      case 'excellent':
        return {
          emoji: '🎉',
          title: '完美理解！',
          color: '#10b981',
          message: '你完全掌握了一一对应的概念，表现非常出色！'
        };
      case 'good':
        return {
          emoji: '👍',
          title: '理解良好！',
          color: '#3b82f6',
          message: '你基本掌握了概念，继续加油！'
        };
      default:
        return {
          emoji: '💪',
          title: '继续练习！',
          color: '#f59e0b',
          message: '多练习几次，你一定能掌握这个概念！'
        };
    }
  };

  const understandingInfo = getUnderstandingInfo();

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}分${secs}秒`;
  };

  const completionTime = result.finalResult?.completionTime || 0;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '500px',
        width: '90%',
        boxShadow: '0 20px 50px rgba(0, 0, 0, 0.3)',
        textAlign: 'center',
        animation: 'modalSlideIn 0.3s ease'
      }}>
        {/* 标题和图标 */}
        <div style={{
          fontSize: '4rem',
          marginBottom: '1rem'
        }}>
          {understandingInfo.emoji}
        </div>
        
        <h2 style={{
          color: understandingInfo.color,
          margin: '0 0 1rem 0',
          fontSize: '2rem'
        }}>
          {understandingInfo.title}
        </h2>

        <p style={{
          color: '#64748b',
          fontSize: '1.1rem',
          marginBottom: '2rem',
          lineHeight: 1.6
        }}>
          {understandingInfo.message}
        </p>

        {/* 成绩统计 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem',
          padding: '1.5rem',
          background: '#f8fafc',
          borderRadius: '12px'
        }}>
          <div>
            <div style={{
              fontSize: '0.875rem',
              color: '#64748b',
              marginBottom: '0.25rem'
            }}>
              准确率
            </div>
            <div style={{
              fontSize: '1.5rem',
              fontWeight: '700',
              color: accuracy >= 80 ? '#10b981' : accuracy >= 60 ? '#f59e0b' : '#ef4444'
            }}>
              {Math.round(accuracy)}%
            </div>
          </div>
          
          <div>
            <div style={{
              fontSize: '0.875rem',
              color: '#64748b',
              marginBottom: '0.25rem'
            }}>
              完成时间
            </div>
            <div style={{
              fontSize: '1.5rem',
              fontWeight: '700',
              color: '#0369a1'
            }}>
              {formatTime(completionTime)}
            </div>
          </div>
          
          <div>
            <div style={{
              fontSize: '0.875rem',
              color: '#64748b',
              marginBottom: '0.25rem'
            }}>
              最终分数
            </div>
            <div style={{
              fontSize: '1.5rem',
              fontWeight: '700',
              color: '#7c3aed'
            }}>
              {result.score}
            </div>
          </div>
        </div>

        {/* 错误分析 */}
        {result.mistakeDetails && (
          result.mistakeDetails.incorrectMappings.length > 0 || 
          result.mistakeDetails.missedMappings.length > 0 || 
          result.mistakeDetails.extraMappings.length > 0
        ) && (
          <div style={{
            background: '#fef3c7',
            border: '1px solid #fbbf24',
            borderRadius: '12px',
            padding: '1rem',
            marginBottom: '2rem',
            textAlign: 'left'
          }}>
            <h4 style={{
              margin: '0 0 0.75rem 0',
              color: '#92400e',
              fontSize: '1rem'
            }}>
              📝 学习建议
            </h4>
            
            {result.mistakeDetails.incorrectMappings.length > 0 && (
              <p style={{
                margin: '0.5rem 0',
                color: '#92400e',
                fontSize: '0.9rem'
              }}>
                • 有 {result.mistakeDetails.incorrectMappings.length} 个连接不正确，要仔细观察对应关系
              </p>
            )}
            
            {result.mistakeDetails.missedMappings.length > 0 && (
              <p style={{
                margin: '0.5rem 0',
                color: '#92400e',
                fontSize: '0.9rem'
              }}>
                • 遗漏了 {result.mistakeDetails.missedMappings.length} 个应该连接的配对
              </p>
            )}
            
            {result.mistakeDetails.extraMappings.length > 0 && (
              <p style={{
                margin: '0.5rem 0',
                color: '#92400e',
                fontSize: '0.9rem'
              }}>
                • 有 {result.mistakeDetails.extraMappings.length} 个多余的连接
              </p>
            )}
          </div>
        )}

        {/* 解释说明 */}
        {exercise.explanation && (
          <div style={{
            background: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '12px',
            padding: '1rem',
            marginBottom: '2rem',
            textAlign: 'left'
          }}>
            <h4 style={{
              margin: '0 0 0.75rem 0',
              color: '#0369a1',
              fontSize: '1rem'
            }}>
              💡 知识点解释
            </h4>
            <p style={{
              margin: 0,
              color: '#0369a1',
              fontSize: '0.9rem',
              lineHeight: 1.5
            }}>
              {exercise.explanation}
            </p>
          </div>
        )}

        {/* 操作按钮 */}
        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'center'
        }}>
          <button
            onClick={onRestart}
            style={{
              background: understandingInfo.color,
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.75rem 2rem',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            再试一次
          </button>
          
          <button
            onClick={onClose}
            style={{
              background: '#e5e7eb',
              color: '#6b7280',
              border: 'none',
              borderRadius: '10px',
              padding: '0.75rem 2rem',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#d1d5db';
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#e5e7eb';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            继续学习
          </button>
        </div>
      </div>

      <style>{`
        @keyframes modalSlideIn {
          from {
            transform: scale(0.9) translateY(-50px);
            opacity: 0;
          }
          to {
            transform: scale(1) translateY(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default ResultModal;