import React from 'react';
import type { CorrespondenceExercise, CorrespondenceGameSession } from '../../types';

interface RopeKnotGameProps {
  exercise: CorrespondenceExercise;
  gameSession: CorrespondenceGameSession;
  onRestart: () => void;
}

const RopeKnotGame: React.FC<RopeKnotGameProps> = () => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px',
      textAlign: 'center'
    }}>
      <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>🪢</div>
      <h2 style={{ color: '#0369a1', marginBottom: '1rem' }}>结绳计数</h2>
      <p style={{ color: '#64748b', fontSize: '1.1rem', marginBottom: '2rem', maxWidth: '500px' }}>
        这个场景正在开发中，敬请期待！结绳计数将让你体验古代人们用绳结记录数量的智慧方法。
      </p>
      <div style={{
        background: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem',
        maxWidth: '400px'
      }}>
        <h4 style={{ margin: '0 0 1rem 0', color: '#92400e' }}>即将推出的特性：</h4>
        <ul style={{ 
          margin: 0, 
          padding: '0 0 0 1.5rem', 
          color: '#92400e',
          textAlign: 'left'
        }}>
          <li>绳子和绳结的视觉对应</li>
          <li>古代计数方法的学习</li>
          <li>渐进式难度挑战</li>
          <li>文化历史小知识</li>
        </ul>
      </div>
      <button
        onClick={() => window.history.back()}
        style={{
          background: '#0ea5e9',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          padding: '0.75rem 2rem',
          fontSize: '1rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#0284c7';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = '#0ea5e9';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        返回选择其他场景
      </button>
    </div>
  );
};

export default RopeKnotGame;