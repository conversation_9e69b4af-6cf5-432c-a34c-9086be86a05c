import React from 'react';
import type { CorrespondenceExercise, CorrespondenceGameSession } from '../../types';

interface ConnectMasterGameProps {
  exercise: CorrespondenceExercise;
  gameSession: CorrespondenceGameSession;
  onRestart: () => void;
}

const ConnectMasterGame: React.FC<ConnectMasterGameProps> = () => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px',
      textAlign: 'center'
    }}>
      <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>🔗</div>
      <h2 style={{ color: '#0369a1', marginBottom: '1rem' }}>连线大师</h2>
      <p style={{ color: '#64748b', fontSize: '1.1rem', marginBottom: '2rem', maxWidth: '500px' }}>
        这个高级场景正在开发中，敬请期待！连线大师将带来更复杂的对应关系挑战，训练你的逻辑思维能力。
      </p>
      <div style={{
        background: '#fee2e2',
        border: '1px solid #fecaca',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem',
        maxWidth: '400px'
      }}>
        <h4 style={{ margin: '0 0 1rem 0', color: '#dc2626' }}>高级特性预览：</h4>
        <ul style={{ 
          margin: 0, 
          padding: '0 0 0 1.5rem', 
          color: '#dc2626',
          textAlign: 'left'
        }}>
          <li>多对多的复杂映射关系</li>
          <li>逻辑推理和分类思维</li>
          <li>动态难度调整算法</li>
          <li>思维过程可视化</li>
        </ul>
      </div>
      <button
        onClick={() => window.history.back()}
        style={{
          background: '#ef4444',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          padding: '0.75rem 2rem',
          fontSize: '1rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#dc2626';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = '#ef4444';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        返回选择其他场景
      </button>
    </div>
  );
};

export default ConnectMasterGame;