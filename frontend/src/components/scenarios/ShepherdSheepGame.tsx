import { useState, useEffect } from 'react';
import GameArea from '../game/GameArea';
import ScorePanel from '../game/ScorePanel';
import ResultModal from '../game/ResultModal';
import { CorrespondenceAPI, handleAPIError } from '../../api/correspondenceApi';
import type { 
  CorrespondenceExercise, 
  CorrespondenceGameSession, 
  CorrespondenceMapping 
} from '../../types';

interface ShepherdSheepGameProps {
  exercise: CorrespondenceExercise;
  gameSession: CorrespondenceGameSession;
  onRestart: () => void;
}

const ShepherdSheepGame: React.FC<ShepherdSheepGameProps> = ({ 
  exercise, 
  gameSession: initialSession, 
  onRestart 
}) => {
  const [gameSession, setGameSession] = useState(initialSession);
  const [userMappings, setUserMappings] = useState<CorrespondenceMapping[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [resultData, setResultData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 更新游戏会话状态
  useEffect(() => {
    setGameSession(initialSession);
    setUserMappings([]);
    setShowResult(false);
    setResultData(null);
    setError(null);
  }, [initialSession]);

  // 处理连线操作
  const handleConnection = (leftItemId: string, rightItemId: string) => {
    // 检查是否已存在该连线
    const existingMapping = userMappings.find(
      m => m.leftItemId === leftItemId || m.rightItemId === rightItemId
    );

    if (existingMapping) {
      // 移除已存在的连线
      setUserMappings(prev => prev.filter(
        m => m.leftItemId !== leftItemId && m.rightItemId !== rightItemId
      ));
    } else {
      // 添加新连线
      const newMapping: CorrespondenceMapping = {
        leftItemId,
        rightItemId,
        isCorrect: false, // 将由服务器验证
        connectionType: 'line'
      };
      setUserMappings(prev => [...prev, newMapping]);
    }
  };

  // 提交答案
  const handleSubmit = async () => {
    if (userMappings.length === 0) {
      setError('请先进行连线操作');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const response = await CorrespondenceAPI.submitMapping(
        gameSession.id,
        { mappings: userMappings }
      );

      if (response.success && response.data) {
        setGameSession(response.data);
        setResultData(response.data);
        setShowResult(true);
      } else {
        throw new Error(response.message || '提交失败');
      }
    } catch (err: any) {
      console.error('提交答案失败:', err);
      setError(handleAPIError(err));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 获取提示
  const handleGetHint = () => {
    if (exercise.hints && exercise.hints.length > 0) {
      const randomHint = exercise.hints[Math.floor(Math.random() * exercise.hints.length)];
      alert(`💡 提示：${randomHint}`);
    }
  };

  // 关闭结果模态框
  const handleCloseResult = () => {
    setShowResult(false);
    if (resultData?.isCompleted) {
      onRestart(); // 如果完成了，重新开始
    }
  };

  return (
    <div className="shepherd-sheep-game">
      {/* 游戏说明 */}
      <div style={{
        background: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '8px',
        padding: '1rem',
        marginBottom: '2rem',
        textAlign: 'center'
      }}>
        <h3 style={{ margin: '0 0 0.5rem 0', color: '#92400e' }}>
          🐑 牧羊人数羊
        </h3>
        <p style={{ margin: 0, color: '#92400e' }}>
          {exercise.description || '帮助牧羊人为每只羊找到对应的小石子，理解一一对应的概念'}
        </p>
      </div>

      {/* 分数面板 */}
      <ScorePanel 
        session={gameSession}
        mappingCount={userMappings.length}
        expectedCount={exercise.content.expectedMappings.length}
      />

      {/* 游戏区域 */}
      <GameArea
        exercise={exercise}
        userMappings={userMappings}
        onConnection={handleConnection}
        scenario="shepherd-sheep"
      />

      {/* 操作按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '1rem',
        marginTop: '2rem'
      }}>
        <button
          onClick={handleGetHint}
          disabled={isSubmitting}
          style={{
            background: '#fbbf24',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '0.75rem 1.5rem',
            fontSize: '1rem',
            cursor: isSubmitting ? 'not-allowed' : 'pointer',
            opacity: isSubmitting ? 0.6 : 1
          }}
        >
          💡 获取提示
        </button>
        
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || userMappings.length === 0}
          style={{
            background: userMappings.length === 0 ? '#9ca3af' : '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '0.75rem 1.5rem',
            fontSize: '1rem',
            cursor: (isSubmitting || userMappings.length === 0) ? 'not-allowed' : 'pointer',
            opacity: (isSubmitting || userMappings.length === 0) ? 0.6 : 1
          }}
        >
          {isSubmitting ? '提交中...' : '提交答案'}
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div style={{
          background: '#fee2e2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          padding: '1rem',
          marginTop: '1rem',
          color: '#dc2626',
          textAlign: 'center'
        }}>
          {error}
        </div>
      )}

      {/* 结果模态框 */}
      {showResult && resultData && (
        <ResultModal
          result={resultData}
          exercise={exercise}
          onClose={handleCloseResult}
          onRestart={onRestart}
        />
      )}
    </div>
  );
};

export default ShepherdSheepGame;