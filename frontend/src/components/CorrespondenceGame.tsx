import { useState, useEffect } from 'react';
import Shepherd<PERSON>heepGame from './scenarios/ShepherdSheepGame';
import RopeKnotGame from './scenarios/RopeKnotGame';
import ConnectMasterGame from './scenarios/ConnectMasterGame';
import { CorrespondenceAPI, generateTempStudentId, handleAPIError } from '../api/correspondenceApi';
import type { CorrespondenceScenario, CorrespondenceExercise, CorrespondenceGameSession } from '../types';

interface CorrespondenceGameProps {
  scenario: CorrespondenceScenario;
  onBackToMenu: () => void;
}

const CorrespondenceGame: React.FC<CorrespondenceGameProps> = ({ scenario, onBackToMenu }) => {
  const [exercise, setExercise] = useState<CorrespondenceExercise | null>(null);
  const [gameSession, setGameSession] = useState<CorrespondenceGameSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取场景标题
  const getScenarioTitle = (scenario: CorrespondenceScenario): string => {
    const titles: Record<CorrespondenceScenario, string> = {
      'shepherd-sheep': '牧羊人数羊',
      'rope-knot': '结绳计数',
      'connect-master': '连线大师',
      'custom': '自定义场景'
    };
    return titles[scenario];
  };

  // 初始化游戏
  useEffect(() => {
    initializeGame();
  }, [scenario]);

  const initializeGame = async () => {
    try {
      setLoading(true);
      setError(null);

      // 首先创建一个练习
      const exerciseResponse = await CorrespondenceAPI.createExercise({
        chapterId: 1,
        subType: 'one-to-one-mapping',
        difficulty: scenario === 'shepherd-sheep' ? 1 : scenario === 'rope-knot' ? 2 : 3,
        title: `${getScenarioTitle(scenario)}练习`,
        description: `学习一一对应概念的${getScenarioTitle(scenario)}游戏`,
        scenario: scenario,
        leftItemCount: 4 + Math.floor(Math.random() * 3), // 4-6个物品
        rightItemCount: 4 + Math.floor(Math.random() * 3), // 4-6个物品
      });

      if (!exerciseResponse.success || !exerciseResponse.data) {
        throw new Error(exerciseResponse.message || '创建练习失败');
      }

      setExercise(exerciseResponse.data);

      // 开始游戏会话
      const studentId = generateTempStudentId();
      const sessionResponse = await CorrespondenceAPI.startGame({
        studentId,
        exerciseId: exerciseResponse.data.id
      });

      if (!sessionResponse.success || !sessionResponse.data) {
        throw new Error(sessionResponse.message || '开始游戏失败');
      }

      setGameSession(sessionResponse.data);
    } catch (err: any) {
      console.error('初始化游戏失败:', err);
      setError(handleAPIError(err));
    } finally {
      setLoading(false);
    }
  };

  // 重新开始游戏
  const handleRestart = () => {
    initializeGame();
  };

  // 渲染对应的游戏组件
  const renderGameComponent = () => {
    if (!exercise || !gameSession) return null;

    const commonProps = {
      exercise,
      gameSession,
      onRestart: handleRestart,
    };

    switch (scenario) {
      case 'shepherd-sheep':
        return <ShepherdSheepGame {...commonProps} />;
      case 'rope-knot':
        return <RopeKnotGame {...commonProps} />;
      case 'connect-master':
        return <ConnectMasterGame {...commonProps} />;
      default:
        return <div>暂未实现的场景</div>;
    }
  };

  if (loading) {
    return (
      <div className="correspondence-game">
        <div className="game-header">
          <h2 className="game-title">{getScenarioTitle(scenario)}</h2>
          <button className="back-button" onClick={onBackToMenu}>
            返回主菜单
          </button>
        </div>
        <div className="game-area" style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          fontSize: '1.2rem',
          color: '#64748b'
        }}>
          正在准备游戏...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="correspondence-game">
        <div className="game-header">
          <h2 className="game-title">{getScenarioTitle(scenario)}</h2>
          <button className="back-button" onClick={onBackToMenu}>
            返回主菜单
          </button>
        </div>
        <div className="game-area" style={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center',
          gap: '1rem'
        }}>
          <div style={{ color: '#ef4444', fontSize: '1.2rem' }}>
            游戏初始化失败
          </div>
          <div style={{ color: '#64748b', textAlign: 'center' }}>
            {error}
          </div>
          <button 
            onClick={handleRestart}
            style={{
              background: '#0ea5e9',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '1rem',
              cursor: 'pointer',
              marginTop: '1rem'
            }}
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="correspondence-game">
      <div className="game-header">
        <h2 className="game-title">{getScenarioTitle(scenario)}</h2>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button className="back-button" onClick={handleRestart}>
            重新开始
          </button>
          <button className="back-button" onClick={onBackToMenu}>
            返回主菜单
          </button>
        </div>
      </div>
      <div className="game-area">
        {renderGameComponent()}
      </div>
    </div>
  );
};

export default CorrespondenceGame;