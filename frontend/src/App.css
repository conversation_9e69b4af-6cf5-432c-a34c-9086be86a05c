/* 重置根节点样式 */
#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 应用整体布局 */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  display: flex;
  flex-direction: column;
}

.app-header {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  color: #0369a1;
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.app-header p {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

.app-main {
  flex: 1;
  padding: 2rem 1rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

/* 场景选择器样式 */
.scenario-selector {
  text-align: center;
  max-width: 900px;
  width: 100%;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.scenario-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #0ea5e9;
}

.scenario-card h3 {
  color: #0369a1;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.scenario-card p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.scenario-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* 游戏组件样式 */
.correspondence-game {
  max-width: 1200px;
  width: 100%;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.game-title {
  font-size: 1.5rem;
  color: #0369a1;
  margin: 0;
}

.back-button {
  background: #e2e8f0;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: #475569;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #cbd5e1;
  transform: translateY(-1px);
}

.game-area {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  min-height: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }
  
  .app-header p {
    font-size: 1rem;
  }
  
  .scenario-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .scenario-card {
    padding: 1.5rem;
  }
  
  .game-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
