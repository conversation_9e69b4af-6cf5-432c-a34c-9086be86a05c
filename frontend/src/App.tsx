import { useState } from 'react';
import CorrespondenceGame from './components/CorrespondenceGame';
import ScenarioSelector from './components/ScenarioSelector';
import type { CorrespondenceScenario } from './types';
import './App.css';

function App() {
  const [selectedScenario, setSelectedScenario] = useState<CorrespondenceScenario | null>(null);

  const handleScenarioSelect = (scenario: CorrespondenceScenario) => {
    setSelectedScenario(scenario);
  };

  const handleBackToMenu = () => {
    setSelectedScenario(null);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>好学趣数学 - 对应计数</h1>
        <p>通过游戏学习一一对应的数学概念</p>
      </header>

      <main className="app-main">
        {!selectedScenario ? (
          <ScenarioSelector onScenarioSelect={handleScenarioSelect} />
        ) : (
          <CorrespondenceGame 
            scenario={selectedScenario}
            onBackToMenu={handleBackToMenu}
          />
        )}
      </main>
    </div>
  );
}

export default App;
