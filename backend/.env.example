# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/hxq_math

# 前端URL配置
FRONTEND_URL=http://localhost:5173

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Redis配置
REDIS_URL=redis://localhost:6379

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10MB

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# API限制配置
API_RATE_LIMIT=100
API_RATE_WINDOW=15

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 云存储配置（可选）
OSS_ACCESS_KEY_ID=your-oss-access-key
OSS_ACCESS_KEY_SECRET=your-oss-secret
OSS_BUCKET=hxq-math-assets
OSS_REGION=oss-cn-hangzhou
EOF < /dev/null