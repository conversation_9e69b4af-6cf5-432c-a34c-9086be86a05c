import { CorrespondenceService } from '../../src/services/CorrespondenceService';
import { CorrespondenceExercise, CorrespondenceGameSession } from '../../src/models/CorrespondenceExercise';

// Mock mongoose models
jest.mock('../../src/models/CorrespondenceExercise');

describe('CorrespondenceService', () => {
  let correspondenceService: CorrespondenceService;
  
  beforeEach(() => {
    correspondenceService = new CorrespondenceService();
    jest.clearAllMocks();
  });
  
  describe('createExercise', () => {
    it('应该成功创建牧羊人数羊练习', async () => {
      const mockExerciseData = {
        chapterId: 1,
        subType: 'one-to-one-mapping' as const,
        difficulty: 2 as const,
        title: '牧羊人数羊',
        description: '帮助牧羊人用石头数羊',
        scenario: 'shepherd-sheep' as const,
        leftItemCount: 5,
        rightItemCount: 4
      };
      
      const mockSavedExercise = {
        id: 'test-exercise-id',
        ...mockExerciseData,
        correctAnswer: {
          comparison: 'left-more',
          count: { left: 5, right: 4, difference: 1 }
        }
      };
      
      // Mock mongoose save method
      (CorrespondenceExercise as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockSavedExercise)
      }));
      
      const result = await correspondenceService.createExercise(mockExerciseData);
      
      expect(result).toBeDefined();
      expect(result.id).toBe('test-exercise-id');
      expect(result.correctAnswer.comparison).toBe('left-more');
    });
    
    it('应该正确计算相等情况', async () => {
      const mockExerciseData = {
        chapterId: 1,
        subType: 'one-to-one-mapping' as const,
        difficulty: 1 as const,
        title: '连线游戏',
        description: '将左右物品一一对应',
        scenario: 'connect-master' as const,
        leftItemCount: 3,
        rightItemCount: 3
      };
      
      const mockSavedExercise = {
        id: 'test-exercise-id-2',
        ...mockExerciseData,
        correctAnswer: {
          comparison: 'equal',
          count: { left: 3, right: 3, difference: 0 }
        }
      };
      
      (CorrespondenceExercise as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockSavedExercise)
      }));
      
      const result = await correspondenceService.createExercise(mockExerciseData);
      
      expect(result.correctAnswer.comparison).toBe('equal');
      expect(result.correctAnswer.count.difference).toBe(0);
    });
  });
  
  describe('startGameSession', () => {
    it('应该成功开始游戏会话', async () => {
      const mockExercise = {
        id: 'exercise-123',
        title: '测试练习',
        description: '测试描述',
        content: {
          scenario: 'shepherd-sheep',
          leftItems: [{ id: 'left-1', type: 'stone' }],
          rightItems: [{ id: 'right-1', type: 'sheep' }],
          allowPartialMapping: false
        }
      };
      
      const mockSession = {
        id: 'session-123',
        save: jest.fn().mockResolvedValue(true)
      };
      
      // Mock service methods
      jest.spyOn(correspondenceService, 'getExercise').mockResolvedValue(mockExercise as any);
      (CorrespondenceGameSession as any).mockImplementation(() => mockSession);
      
      const result = await correspondenceService.startGameSession({
        studentId: 'student-123',
        exerciseId: 'exercise-123'
      });
      
      expect(result).toBeDefined();
      expect(result.sessionId).toBeDefined();
      expect(result.exercise).toBeDefined();
      expect(result.exercise.scenario).toBe('shepherd-sheep');
    });
    
    it('练习不存在时应该抛出错误', async () => {
      jest.spyOn(correspondenceService, 'getExercise').mockResolvedValue(null);
      
      await expect(correspondenceService.startGameSession({
        studentId: 'student-123',
        exerciseId: 'non-existent'
      })).rejects.toThrow('练习不存在');
    });
  });
  
  describe('submitMapping', () => {
    it('应该正确验证完全正确的映射', async () => {
      const mockSession = {
        id: 'session-123',
        exerciseId: 'exercise-123',
        startTime: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };
      
      const mockExercise = {
        id: 'exercise-123',
        correctAnswer: {
          mappings: [
            { leftItemId: 'left-1', rightItemId: 'right-1', isCorrect: true },
            { leftItemId: 'left-2', rightItemId: 'right-2', isCorrect: true }
          ],
          comparison: 'equal',
          count: { left: 2, right: 2, difference: 0 }
        },
        content: { scenario: 'shepherd-sheep' }
      };
      
      const userMappings = [
        { leftItemId: 'left-1', rightItemId: 'right-1', isCorrect: true, connectionType: 'line' },
        { leftItemId: 'left-2', rightItemId: 'right-2', isCorrect: true, connectionType: 'line' }
      ];
      
      // Mock database queries
      (CorrespondenceGameSession.findOne as jest.Mock).mockResolvedValue(mockSession);
      jest.spyOn(correspondenceService, 'getExercise').mockResolvedValue(mockExercise as any);
      
      const result = await correspondenceService.submitMapping({
        sessionId: 'session-123',
        mappings: userMappings as any
      });
      
      expect(result.isCorrect).toBe(true);
      expect(result.accuracy).toBe(1);
      expect(result.score).toBe(100);
    });
    
    it('应该正确识别部分错误的映射', async () => {
      const mockSession = {
        id: 'session-123',
        exerciseId: 'exercise-123',
        startTime: new Date(),
        attemptCount: 0,
        save: jest.fn().mockResolvedValue(true)
      };
      
      const mockExercise = {
        id: 'exercise-123',
        correctAnswer: {
          mappings: [
            { leftItemId: 'left-1', rightItemId: 'right-1', isCorrect: true },
            { leftItemId: 'left-2', rightItemId: 'right-2', isCorrect: true }
          ],
          comparison: 'equal',
          count: { left: 2, right: 2, difference: 0 }
        },
        content: { scenario: 'shepherd-sheep' }
      };
      
      const userMappings = [
        { leftItemId: 'left-1', rightItemId: 'right-2', isCorrect: false, connectionType: 'line' }, // 错误连接
        { leftItemId: 'left-2', rightItemId: 'right-1', isCorrect: false, connectionType: 'line' }  // 错误连接
      ];
      
      (CorrespondenceGameSession.findOne as jest.Mock).mockResolvedValue(mockSession);
      jest.spyOn(correspondenceService, 'getExercise').mockResolvedValue(mockExercise as any);
      
      const result = await correspondenceService.submitMapping({
        sessionId: 'session-123',
        mappings: userMappings as any
      });
      
      expect(result.isCorrect).toBe(false);
      expect(result.accuracy).toBe(0);
      expect(result.score).toBe(0);
    });
  });
});