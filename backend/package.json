{"name": "hxq-math-backend", "version": "1.0.0", "description": "一二年级数学学习辅助App后端系统", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "prepare": "husky install", "docker:build": "docker build -t hxq-math-backend .", "docker:run": "docker run -p 3000:3000 hxq-math-backend"}, "keywords": ["education", "math", "children", "api", "nodejs", "typescript", "mongodb"], "author": "Backend Architect", "license": "MIT", "dependencies": {"apollo-server-express": "^3.12.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dataloader": "^2.2.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "graphql": "^16.8.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "redis": "^4.6.11", "sharp": "^0.33.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "concurrently": "^9.2.0", "eslint": "^8.55.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "rimraf": "^6.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsc-alias": "^1.8.8", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"]}}