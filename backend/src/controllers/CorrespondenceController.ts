import { Request, Response } from 'express';
import { CorrespondenceService } from '../services/CorrespondenceService';
import { validationResult } from 'express-validator';
import { logger } from '../utils/logger';

export class CorrespondenceController {
  private correspondenceService: CorrespondenceService;
  
  constructor() {
    this.correspondenceService = new CorrespondenceService();
  }
  
  /**
   * 创建对应计数练习
   */
  createExercise = async (req: Request, res: Response): Promise<void> => {
    try {
      // 验证输入数据
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
        return;
      }
      
      const exerciseData = req.body;
      const exercise = await this.correspondenceService.createExercise(exerciseData);
      
      logger.info(`Created correspondence exercise: ${exercise.id}`);
      
      res.status(201).json({
        success: true,
        message: '练习创建成功',
        data: exercise
      });
    } catch (error) {
      logger.error('Error creating correspondence exercise', error);
      res.status(500).json({
        success: false,
        message: '创建练习失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 获取练习列表
   */
  getExercises = async (req: Request, res: Response): Promise<void> => {
    try {
      const { chapterId, difficulty } = req.query;
      
      const exercises = await this.correspondenceService.getExercises(
        chapterId ? Number(chapterId) : undefined,
        difficulty ? Number(difficulty) : undefined
      );
      
      res.json({
        success: true,
        message: '获取练习列表成功',
        data: exercises,
        count: exercises.length
      });
    } catch (error) {
      logger.error('Error fetching correspondence exercises', error);
      res.status(500).json({
        success: false,
        message: '获取练习列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 获取单个练习
   */
  getExercise = async (req: Request, res: Response): Promise<void> => {
    try {
      const exerciseId = req.params.exerciseId!;
      
      const exercise = await this.correspondenceService.getExercise(exerciseId);
      
      if (!exercise) {
        res.status(404).json({
          success: false,
          message: '练习不存在'
        });
        return;
      }
      
      res.json({
        success: true,
        message: '获取练习成功',
        data: exercise
      });
    } catch (error) {
      logger.error('Error fetching correspondence exercise', error);
      res.status(500).json({
        success: false,
        message: '获取练习失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 开始游戏会话
   */
  startGame = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
        return;
      }
      
      const { studentId, exerciseId } = req.body;
      
      const gameSession = await this.correspondenceService.startGameSession({
        studentId,
        exerciseId
      });
      
      logger.info(`Started correspondence game session: ${gameSession.sessionId}`);
      
      res.json({
        success: true,
        message: '游戏开始成功',
        data: gameSession
      });
    } catch (error) {
      logger.error('Error starting correspondence game', error);
      res.status(500).json({
        success: false,
        message: '开始游戏失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 提交映射答案
   */
  submitMapping = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
        return;
      }
      
      const sessionId = req.params.sessionId!;
      const { mappings } = req.body;
      
      const result = await this.correspondenceService.submitMapping({
        sessionId,
        mappings
      });
      
      logger.info(`Submitted correspondence mapping - Session: ${sessionId}, Correct: ${result.isCorrect}`);
      
      res.json({
        success: true,
        message: '答案提交成功',
        data: result
      });
    } catch (error) {
      logger.error('Error submitting correspondence mapping', error);
      res.status(500).json({
        success: false,
        message: '提交答案失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 获取游戏会话详情
   */
  getGameSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const sessionId = req.params.sessionId!;
      
      const session = await this.correspondenceService.getGameSession(sessionId);
      
      if (!session) {
        res.status(404).json({
          success: false,
          message: '游戏会话不存在'
        });
        return;
      }
      
      res.json({
        success: true,
        message: '获取游戏会话成功',
        data: session
      });
    } catch (error) {
      logger.error('Error fetching game session', error);
      res.status(500).json({
        success: false,
        message: '获取游戏会话失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 获取学生练习历史
   */
  getStudentHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const studentId = req.params.studentId!;
      const { chapterId } = req.query;
      
      const history = await this.correspondenceService.getStudentHistory(
        studentId,
        chapterId ? Number(chapterId) : undefined
      );
      
      res.json({
        success: true,
        message: '获取学习历史成功',
        data: history,
        count: history.length
      });
    } catch (error) {
      logger.error('Error fetching student history', error);
      res.status(500).json({
        success: false,
        message: '获取学习历史失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
  
  /**
   * 获取学习统计数据
   */
  getAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { studentId, startDate, endDate } = req.query;
      
      // TODO: 实现详细的学习分析功能
      // 这里可以添加更复杂的数据分析逻辑
      
      res.json({
        success: true,
        message: '获取分析数据成功',
        data: {
          // 占位数据，实际应该从数据库聚合查询
          totalExercises: 0,
          completedExercises: 0,
          averageAccuracy: 0,
          totalTimeSpent: 0,
          conceptsProgress: {}
        }
      });
    } catch (error) {
      logger.error('Error fetching analytics', error);
      res.status(500).json({
        success: false,
        message: '获取分析数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
}

export default new CorrespondenceController();