import { Router } from 'express';
import { body, param, query } from 'express-validator';
import correspondenceController from '../controllers/CorrespondenceController';

const router = Router();

/**
 * 对应计数模块路由
 */

// 创建练习 - POST /api/correspondence/exercises
router.post('/exercises',
  // 输入验证
  [
    body('chapterId')
      .isInt({ min: 1 })
      .withMessage('章节ID必须是正整数'),
    body('subType')
      .isIn(['one-to-one-mapping', 'counting-comparison', 'visual-correspondence'])
      .withMessage('子类型必须是有效值'),
    body('difficulty')
      .isInt({ min: 1, max: 5 })
      .withMessage('难度必须是1-5之间的整数'),
    body('title')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('标题长度必须在1-100字符之间'),
    body('description')
      .trim()
      .isLength({ min: 1, max: 500 })
      .withMessage('描述长度必须在1-500字符之间'),
    body('scenario')
      .isIn(['shepherd-sheep', 'rope-knot', 'connect-master', 'custom'])
      .withMessage('场景必须是有效值'),
    body('leftItemCount')
      .isInt({ min: 1, max: 20 })
      .withMessage('左侧物品数量必须在1-20之间'),
    body('rightItemCount')
      .isInt({ min: 1, max: 20 })
      .withMessage('右侧物品数量必须在1-20之间'),
    body('customItems')
      .optional()
      .isObject()
      .withMessage('自定义物品配置必须是对象')
  ],
  correspondenceController.createExercise
);

// 获取练习列表 - GET /api/correspondence/exercises
router.get('/exercises',
  // 查询参数验证
  [
    query('chapterId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('章节ID必须是正整数'),
    query('difficulty')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('难度必须是1-5之间的整数')
  ],
  correspondenceController.getExercises
);

// 获取单个练习 - GET /api/correspondence/exercises/:exerciseId
router.get('/exercises/:exerciseId',
  [
    param('exerciseId')
      .isUUID()
      .withMessage('练习ID必须是有效的UUID')
  ],
  correspondenceController.getExercise
);

// 开始游戏 - POST /api/correspondence/games/start
router.post('/games/start',
  [
    body('studentId')
      .isUUID()
      .withMessage('学生ID必须是有效的UUID'),
    body('exerciseId')
      .isUUID()
      .withMessage('练习ID必须是有效的UUID')
  ],
  correspondenceController.startGame
);

// 提交答案 - POST /api/correspondence/games/:sessionId/submit
router.post('/games/:sessionId/submit',
  [
    param('sessionId')
      .isUUID()
      .withMessage('会话ID必须是有效的UUID'),
    body('mappings')
      .isArray()
      .withMessage('映射必须是数组'),
    body('mappings.*.leftItemId')
      .isString()
      .withMessage('左侧物品ID必须是字符串'),
    body('mappings.*.rightItemId')
      .isString()
      .withMessage('右侧物品ID必须是字符串'),
    body('mappings.*.connectionType')
      .optional()
      .isIn(['line', 'drag-drop', 'gesture'])
      .withMessage('连接类型必须是有效值')
  ],
  correspondenceController.submitMapping
);

// 获取游戏会话 - GET /api/correspondence/games/:sessionId
router.get('/games/:sessionId',
  [
    param('sessionId')
      .isUUID()
      .withMessage('会话ID必须是有效的UUID')
  ],
  correspondenceController.getGameSession
);

// 获取学生练习历史 - GET /api/correspondence/students/:studentId/history
router.get('/students/:studentId/history',
  [
    param('studentId')
      .isUUID()
      .withMessage('学生ID必须是有效的UUID'),
    query('chapterId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('章节ID必须是正整数')
  ],
  correspondenceController.getStudentHistory
);

// 获取学习分析数据 - GET /api/correspondence/analytics
router.get('/analytics',
  [
    query('studentId')
      .optional()
      .isUUID()
      .withMessage('学生ID必须是有效的UUID'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('开始日期必须是有效的ISO8601格式'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('结束日期必须是有效的ISO8601格式')
  ],
  correspondenceController.getAnalytics
);

export default router;