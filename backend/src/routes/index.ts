import { Router } from 'express';
import correspondenceRoutes from './correspondence';

const router = Router();

// API版本前缀
const API_VERSION = '/api/v1';

// 对应计数模块路由
router.use(`${API_VERSION}/correspondence`, correspondenceRoutes);

// 健康检查路由
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'hxq-math-backend',
    version: '1.0.0'
  });
});

// API文档路由（简单版本，后续可以集成Swagger）
router.get('/api', (req, res) => {
  res.json({
    message: '一二年级数学学习辅助App API',
    version: '1.0.0',
    modules: [
      {
        name: 'correspondence',
        description: '对应计数模块',
        endpoints: [
          'POST /api/v1/correspondence/exercises - 创建练习',
          'GET /api/v1/correspondence/exercises - 获取练习列表',
          'GET /api/v1/correspondence/exercises/:id - 获取单个练习',
          'POST /api/v1/correspondence/games/start - 开始游戏',
          'POST /api/v1/correspondence/games/:sessionId/submit - 提交答案',
          'GET /api/v1/correspondence/games/:sessionId - 获取游戏会话',
          'GET /api/v1/correspondence/students/:studentId/history - 获取学习历史',
          'GET /api/v1/correspondence/analytics - 获取分析数据'
        ]
      }
    ]
  });
});

export default router;