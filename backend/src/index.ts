import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import mongoose from 'mongoose';
import dotenv from 'dotenv';

import routes from './routes';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './middleware/error';
import { logger } from './utils/logger';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hxq_math';

// 中间件配置
app.use(helmet()); // 安全头部
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(compression()); // 压缩响应
app.use(express.json({ limit: '10mb' })); // JSON解析
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL编码解析

// 请求日志
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});
app.use('/api', limiter);

// 路由
app.use('/', routes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use(ErrorHandler.handle);

// 数据库连接
const connectDB = async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    logger.info('MongoDB连接成功');
  } catch (error) {
    logger.error('MongoDB连接失败', error);
    process.exit(1);
  }
};

// 服务器实例引用
let serverInstance: any = null;

// 优雅关闭
const gracefulShutdown = () => {
  logger.info('正在关闭服务器...');
  
  if (serverInstance) {
    // 关闭HTTP服务器
    serverInstance.close(async () => {
      logger.info('HTTP服务器已关闭');
      
      // 关闭数据库连接
      try {
        await mongoose.connection.close();
        logger.info('数据库连接已关闭');
      } catch (error) {
        logger.error('关闭数据库连接时出错', error);
      }
      
      process.exit(0);
    });
  }
  
  // 强制关闭
  setTimeout(() => {
    logger.error('强制关闭服务器');
    process.exit(1);
  }, 10000);
};

// 启动服务器
const startServer = async () => {
  await connectDB();
  
  serverInstance = app.listen(PORT, () => {
    logger.info(`服务器启动成功 - 端口: ${PORT}`);
  });
  
  // 错误处理
  serverInstance.on('error', (error: any) => {
    if (error.code === 'EADDRINUSE') {
      logger.error(`端口 ${PORT} 已被占用`);
    } else {
      logger.error('服务器启动失败', error);
    }
    process.exit(1);
  });
  
  // 进程信号处理
  process.on('SIGTERM', gracefulShutdown);
  process.on('SIGINT', gracefulShutdown);
  
  return serverInstance;
};

// 全局异常处理
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
});

process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', error);
  process.exit(1);
});

// 导出server实例供测试使用
export const server = startServer();

export default app;