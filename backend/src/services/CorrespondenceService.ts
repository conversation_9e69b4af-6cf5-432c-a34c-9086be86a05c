import { 
  CorrespondenceExercise, 
  CorrespondenceGameSession,
  ICorrespondenceExercise,
  CorrespondenceMapping,
  CorrespondenceItem
} from '../models/CorrespondenceExercise';
import { v4 as uuidv4 } from 'uuid';

export interface CreateExerciseData {
  chapterId: number;
  subType: 'one-to-one-mapping' | 'counting-comparison' | 'visual-correspondence';
  difficulty: 1 | 2 | 3 | 4 | 5;
  title: string;
  description: string;
  scenario: 'shepherd-sheep' | 'rope-knot' | 'connect-master' | 'custom';
  leftItemCount: number;
  rightItemCount: number;
  customItems?: {
    leftType: string;
    rightType: string;
  };
}

export interface StartGameData {
  studentId: string;
  exerciseId: string;
}

export interface SubmitMappingData {
  sessionId: string;
  mappings: CorrespondenceMapping[];
}

export class CorrespondenceService {
  
  /**
   * 创建对应计数练习
   */
  async createExercise(data: CreateExerciseData): Promise<ICorrespondenceExercise> {
    const exerciseId = uuidv4();
    
    // 根据场景生成对应项
    const { leftItems, rightItems } = this.generateItems(
      data.scenario, 
      data.leftItemCount, 
      data.rightItemCount,
      data.customItems
    );
    
    // 生成正确的映射关系
    const correctMappings = this.generateCorrectMappings(leftItems, rightItems);
    
    // 计算比较结果
    const comparison = leftItems.length === rightItems.length ? 'equal' :
                      leftItems.length > rightItems.length ? 'left-more' : 'right-more';
    
    const exercise = new CorrespondenceExercise({
      id: exerciseId,
      chapterId: data.chapterId,
      type: 'correspondence',
      subType: data.subType,
      difficulty: data.difficulty,
      title: data.title,
      description: data.description,
      
      content: {
        scenario: data.scenario,
        leftItems,
        rightItems,
        expectedMappings: correctMappings,
        allowPartialMapping: data.difficulty <= 2,
        showResult: 'auto-detect'
      },
      
      correctAnswer: {
        mappings: correctMappings,
        comparison,
        count: {
          left: leftItems.length,
          right: rightItems.length,
          difference: Math.abs(leftItems.length - rightItems.length)
        }
      },
      
      hints: this.generateHints(data.scenario, comparison),
      explanation: this.generateExplanation(data.scenario, leftItems.length, rightItems.length),
      relatedConcepts: ['一一对应', '计数', '比较大小'],
      
      visualEffects: {
        successAnimation: 'sparkle',
        errorFeedback: 'gentle-shake',
        completionCelebration: 'rainbow-confetti'
      }
    });
    
    return await exercise.save();
  }
  
  /**
   * 获取练习列表
   */
  async getExercises(chapterId?: number, difficulty?: number): Promise<ICorrespondenceExercise[]> {
    const filter: any = {};
    if (chapterId) filter.chapterId = chapterId;
    if (difficulty) filter.difficulty = difficulty;
    
    return await CorrespondenceExercise.find(filter).sort({ difficulty: 1, createdAt: 1 });
  }
  
  /**
   * 获取单个练习
   */
  async getExercise(exerciseId: string): Promise<ICorrespondenceExercise | null> {
    return await CorrespondenceExercise.findOne({ id: exerciseId });
  }
  
  /**
   * 开始游戏会话
   */
  async startGameSession(data: StartGameData): Promise<any> {
    const exercise = await this.getExercise(data.exerciseId);
    if (!exercise) {
      throw new Error('练习不存在');
    }
    
    const sessionId = uuidv4();
    
    const session = new CorrespondenceGameSession({
      id: sessionId,
      studentId: data.studentId,
      exerciseId: data.exerciseId,
      gameType: 'correspondence-counting',
      scenario: exercise.content.scenario,
      startTime: new Date(),
      userMappings: [],
      attemptCount: 0,
      mistakeDetails: {
        incorrectMappings: [],
        missedMappings: [],
        extraMappings: []
      },
      timePerMapping: [],
      hintUsed: false
    });
    
    await session.save();
    
    return {
      sessionId,
      exercise: {
        id: exercise.id,
        title: exercise.title,
        description: exercise.description,
        scenario: exercise.content.scenario,
        leftItems: exercise.content.leftItems,
        rightItems: exercise.content.rightItems,
        allowPartialMapping: exercise.content.allowPartialMapping
      }
    };
  }
  
  /**
   * 提交映射答案
   */
  async submitMapping(data: SubmitMappingData): Promise<any> {
    const session = await CorrespondenceGameSession.findOne({ id: data.sessionId });
    if (!session) {
      throw new Error('游戏会话不存在');
    }
    
    const exercise = await this.getExercise(session.exerciseId);
    if (!exercise) {
      throw new Error('练习不存在');
    }
    
    // 验证答案
    const result = this.validateMappings(data.mappings, exercise.correctAnswer.mappings);
    
    // 更新会话数据
    session.userMappings = data.mappings;
    session.attemptCount += 1;
    session.mistakeDetails = result.mistakeDetails;
    session.score = result.score;
    
    if (result.isCorrect) {
      session.isCompleted = true;
      session.endTime = new Date();
      session.finalResult = {
        accuracy: result.accuracy,
        completionTime: (session.endTime.getTime() - session.startTime.getTime()) / 1000,
        understanding: this.assessUnderstanding(result.accuracy, session.attemptCount)
      };
    }
    
    await session.save();
    
    return {
      isCorrect: result.isCorrect,
      score: result.score,
      accuracy: result.accuracy,
      feedback: this.generateFeedback(result, exercise.content.scenario),
      comparison: exercise.correctAnswer.comparison,
      countResult: exercise.correctAnswer.count,
      isCompleted: session.isCompleted,
      finalResult: session.finalResult
    };
  }
  
  /**
   * 获取游戏会话详情
   */
  async getGameSession(sessionId: string): Promise<any> {
    return await CorrespondenceGameSession.findOne({ id: sessionId });
  }
  
  /**
   * 获取学生的练习历史
   */
  async getStudentHistory(studentId: string, chapterId?: number): Promise<any[]> {
    const filter: any = { studentId };
    if (chapterId) {
      // 需要关联查询获取练习的章节信息
      const exercises = await CorrespondenceExercise.find({ chapterId }).select('id');
      const exerciseIds = exercises.map(e => e.id);
      filter.exerciseId = { $in: exerciseIds };
    }
    
    return await CorrespondenceGameSession.find(filter)
      .sort({ startTime: -1 })
      .populate('exerciseId');
  }
  
  // 私有辅助方法
  
  private generateItems(
    scenario: string, 
    leftCount: number, 
    rightCount: number,
    customItems?: { leftType: string; rightType: string }
  ): { leftItems: CorrespondenceItem[], rightItems: CorrespondenceItem[] } {
    
    const itemConfigs: Record<string, { left: string; right: string }> = {
      'shepherd-sheep': { left: 'stone', right: 'sheep' },
      'rope-knot': { left: 'rope', right: 'knot' },
      'connect-master': { left: 'apple', right: 'box' },
      'custom': customItems ? { left: customItems.leftType, right: customItems.rightType } : { left: 'item', right: 'target' }
    };
    
    const config = itemConfigs[scenario] || itemConfigs['custom'];
    
    const leftItems: CorrespondenceItem[] = Array.from({ length: leftCount }, (_, i) => ({
      id: `left-${i + 1}`,
      type: config!.left as any,
      displayName: `${config!.left} ${i + 1}`,
      imageUrl: `/images/${config!.left}/${i % 3 + 1}.png`,
      position: { x: 50, y: 100 + i * 80 },
      isConnectable: true,
      maxConnections: 1
    }));
    
    const rightItems: CorrespondenceItem[] = Array.from({ length: rightCount }, (_, i) => ({
      id: `right-${i + 1}`,
      type: config!.right as any,
      displayName: `${config!.right} ${i + 1}`,
      imageUrl: `/images/${config!.right}/${i % 3 + 1}.png`,
      position: { x: 350, y: 100 + i * 80 },
      isConnectable: true,
      maxConnections: 1
    }));
    
    return { leftItems, rightItems };
  }
  
  private generateCorrectMappings(
    leftItems: CorrespondenceItem[], 
    rightItems: CorrespondenceItem[]
  ): CorrespondenceMapping[] {
    const mappings: CorrespondenceMapping[] = [];
    const minCount = Math.min(leftItems.length, rightItems.length);
    
    for (let i = 0; i < minCount; i++) {
      mappings.push({
        leftItemId: leftItems[i]!.id,
        rightItemId: rightItems[i]!.id,
        isCorrect: true,
        connectionType: 'line'
      });
    }
    
    return mappings;
  }
  
  private generateHints(scenario: string, comparison: string): string[] {
    const baseHints = [
      '仔细观察左右两边的物品',
      '尝试用线连接相对应的物品',
      '一个对应一个，不要重复连接'
    ];
    
    const comparisonHints: Record<string, string> = {
      'equal': '左右两边的数量是一样的',
      'left-more': '左边的数量比右边多',
      'right-more': '右边的数量比左边多'
    };
    
    const hint = comparisonHints[comparison] ?? '观察数量关系';
    return [...baseHints, hint];
  }
  
  private generateExplanation(scenario: string, leftCount: number, rightCount: number): string {
    const scenarios: Record<string, string> = {
      'shepherd-sheep': `牧羊人用${leftCount}块石头来数${rightCount}只羊`,
      'rope-knot': `用${leftCount}段绳子打${rightCount}个结`,
      'connect-master': `把${leftCount}个苹果放进${rightCount}个盒子`,
      'custom': `左边有${leftCount}个物品，右边有${rightCount}个物品`
    };
    
    let explanation = scenarios[scenario] ?? scenarios['custom']!;
    
    if (leftCount === rightCount) {
      explanation += '，一一对应后正好相等。';
    } else if (leftCount > rightCount) {
      explanation += `，左边多了${leftCount - rightCount}个。`;
    } else {
      explanation += `，右边多了${rightCount - leftCount}个。`;
    }
    
    return explanation;
  }
  
  private validateMappings(
    userMappings: CorrespondenceMapping[], 
    correctMappings: CorrespondenceMapping[]
  ): any {
    const correctSet = new Set(
      correctMappings.map(m => `${m.leftItemId}-${m.rightItemId}`)
    );
    const userSet = new Set(
      userMappings.map(m => `${m.leftItemId}-${m.rightItemId}`)
    );
    
    let correctCount = 0;
    const incorrectMappings: CorrespondenceMapping[] = [];
    
    userMappings.forEach(mapping => {
      const key = `${mapping.leftItemId}-${mapping.rightItemId}`;
      if (correctSet.has(key)) {
        correctCount++;
      } else {
        incorrectMappings.push(mapping);
      }
    });
    
    const missedMappings = correctMappings.filter(correct => {
      const key = `${correct.leftItemId}-${correct.rightItemId}`;
      return !userSet.has(key);
    });
    
    const accuracy = correctMappings.length > 0 ? correctCount / correctMappings.length : 0;
    const isCorrect = correctCount === correctMappings.length && userMappings.length === correctMappings.length;
    
    return {
      isCorrect,
      accuracy,
      score: Math.round(accuracy * 100),
      mistakeDetails: {
        incorrectMappings,
        missedMappings,
        extraMappings: userMappings.filter(user => 
          !correctMappings.some(correct => 
            correct.leftItemId === user.leftItemId && correct.rightItemId === user.rightItemId
          )
        )
      }
    };
  }
  
  private assessUnderstanding(accuracy: number, attemptCount: number): 'excellent' | 'good' | 'needs-practice' {
    if (accuracy >= 0.95 && attemptCount === 1) return 'excellent';
    if (accuracy >= 0.8 && attemptCount <= 2) return 'good';
    return 'needs-practice';
  }
  
  private generateFeedback(result: any, scenario: string): string {
    if (result.isCorrect) {
      return '太棒了！你完美地完成了一一对应！';
    }
    
    if (result.accuracy >= 0.8) {
      return '很好！再检查一下还有哪些没有正确连接。';
    }
    
    if (result.accuracy >= 0.5) {
      return '继续努力！仔细观察左右两边的对应关系。';
    }
    
    return '别着急，让我们重新开始。记住要一个对应一个哦！';
  }
}