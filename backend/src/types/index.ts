import { Request } from 'express';
import { Document, ObjectId } from 'mongoose';

// 基础类型定义
export type UserType = 'teacher' | 'student' | 'parent';
export type ExerciseType = 'drag' | 'select' | 'input' | 'draw' | 'classify' | 'connect' | 
                          'sequence' | 'replace' | 'bracket' | 'multiply' | 'divide' | 'mixedOperation';
export type GameType = 'venn-diagram' | 'enumeration' | 'classification' | 'sequencing';
export type AgeGroup = '6-7' | '7-8' | '8-9';
export type ReviewStatus = 'pending' | 'approved' | 'rejected';

// API响应接口
export interface APIResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
    timestamp: string;
    requestId: string;
  };
  errors?: ErrorDetail[];
}

export interface ErrorDetail {
  field?: string;
  code: string;
  message: string;
}

// 用户相关接口
export interface UserProfile {
  name: string;
  avatar?: string;
  email?: string;
  grade?: number;
  school?: string;
  classId?: string;
  region?: string;
}

export interface SecurityInfo {
  passwordHash: string;
  salt: string;
  lastPasswordChange: Date;
  loginAttempts: number;
  lockUntil?: Date;
}

export interface UserPreferences {
  language: string;
  theme: string;
  notifications: boolean;
  soundEnabled: boolean;
}

export interface PrivacySettings {
  dataProcessingConsent: boolean;
  parentalConsent: boolean;
  consentDate: Date;
}

export interface IUser extends Document {
  _id: ObjectId;
  type: UserType;
  profile: UserProfile;
  security: SecurityInfo;
  preferences: UserPreferences;
  privacy: PrivacySettings;
  createdAt: Date;
  lastActive: Date;
  isActive: boolean;
}

// 学习进度相关接口
export interface Mistake {
  exerciseId: string;
  wrongAnswer: any;
  timestamp: Date;
  mistakeType: string;
}

export interface ChapterProgress {
  chapterId: number;
  completedLessons: string[];
  stars: number;
  accuracy: number;
  timeSpent: number;
  lastAccessed: Date;
  mistakes: Mistake[];
}

export interface Goal {
  week: string;
  targetStars: number;
  achieved: boolean;
}

export interface ILearningProgress extends Document {
  _id: ObjectId;
  studentId: ObjectId;
  chapters: ChapterProgress[];
  totalStars: number;
  achievements: string[];
  currentLevel: number;
  weeklyGoals: Goal[];
  lastLesson: string;
  createdAt: Date;
  updatedAt: Date;
}

// 题目相关接口
export interface ExerciseContent {
  question: string;
  options?: any[];
  visualElements?: any[];
  interactiveElements?: any[];
}

export interface ExerciseMetadata {
  creator: string;
  reviewStatus: ReviewStatus;
  qualityScore: number;
  usage: {
    attempted: number;
    completed: number;
    averageTime: number;
    averageAccuracy: number;
  };
}

export interface IExercise extends Document {
  _id: ObjectId;
  chapterId: number;
  type: ExerciseType;
  subType: string;
  difficulty: number;
  content: ExerciseContent;
  correctAnswer: any;
  hints: string[];
  explanation: string;
  relatedConcepts: string[];
  estimatedTime: number;
  ageGroup: AgeGroup;
  metadata: ExerciseMetadata;
  createdAt: Date;
  updatedAt: Date;
}

// 游戏会话相关接口
export interface GameMetrics {
  clicks: number;
  drags: number;
  hints: number;
  retries: number;
}

export interface GameMistake {
  timestamp: Date;
  exerciseId?: string;
  errorType: string;
  correction: any;
}

export interface IGameSession extends Document {
  _id: ObjectId;
  studentId: ObjectId;
  gameType: GameType;
  gameData: any;
  startTime: Date;
  endTime?: Date;
  score: number;
  maxScore: number;
  duration: number;
  achievements: string[];
  mistakes: GameMistake[];
  gameMetrics: GameMetrics;
  createdAt: Date;
}

// JWT相关接口
export interface JWTPayload {
  userId: string;
  userType: UserType;
  permissions: string[];
  iat: number;
  exp: number;
  jti: string;
}

export interface AuthenticatedRequest extends Request {
  user: JWTPayload;
  traceId: string;
}

// 缓存相关接口
export interface CacheOptions {
  ttl?: number;
  key: string;
  tags?: string[];
}

// 监控相关接口
export interface PerformanceMetrics {
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requestsPerSecond: number;
    peakRPS: number;
  };
  database: {
    connectionPool: number;
    queryTime: number;
    slowQueries: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
  };
  business: {
    activeUsers: number;
    completedExercises: number;
    errorRate: number;
  };
}

// 日志相关接口
export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  service: string;
  traceId: string;
  userId?: string;
  action: string;
  details: any;
  duration?: number;
  metadata: {
    version: string;
    environment: string;
    nodeId: string;
  };
}

// 分页相关接口
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  nextPage?: number;
  prevPage?: number;
}

// 统计分析相关接口
export interface TimeRange {
  start: Date;
  end: Date;
}

export interface LearningAnalytics {
  studentId: string;
  timeRange: TimeRange;
  totalTime: number;
  accuracy: number;
  strongAreas: string[];
  weakAreas: string[];
  trends: Trend[];
}

export interface Trend {
  date: Date;
  value: number;
  metric: string;
}

// GraphQL相关接口
export interface GraphQLContext {
  user?: JWTPayload;
  traceId: string;
  loaders: {
    userLoader: any;
    progressLoader: any;
    exerciseLoader: any;
  };
}

// 验证相关接口
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: ValidationError[];
}