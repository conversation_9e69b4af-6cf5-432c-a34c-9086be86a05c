import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  code?: number;
  details?: any;
  isOperational?: boolean;
}

export class ErrorHandler {
  /**
   * 全局错误处理中间件
   */
  public static handle(
    error: AppError,
    req: Request,
    res: Response,
    next: NextFunction,
  ): void {
    // 记录错误
    logger.error('Unhandled error', error);

    // 确定状态码
    const statusCode = error.statusCode || 500;
    
    // 构建错误响应
    const errorResponse = {
      success: false,
      message: error.message || 'Internal server error',
      timestamp: new Date().toISOString()
    };

    res.status(statusCode).json(errorResponse);
  }

  /**
   * 创建应用错误
   */
  public static createError(
    message: string,
    statusCode = 500,
    code?: number,
    details?: any,
  ): AppError {
    const error = new Error(message) as AppError;
    error.statusCode = statusCode;
    error.code = code;
    error.details = details;
    error.isOperational = true;
    
    return error;
  }
}

// 导出便捷函数
export const createError = ErrorHandler.createError;