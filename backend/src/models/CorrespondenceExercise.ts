import mongoose, { Schema, Document } from 'mongoose';

// 对应计数练习接口
export interface ICorrespondenceExercise extends Document {
  id: string;
  chapterId: number;
  type: 'correspondence';
  subType: 'one-to-one-mapping' | 'counting-comparison' | 'visual-correspondence';
  difficulty: 1 | 2 | 3 | 4 | 5;
  title: string;
  description: string;
  content: {
    scenario: 'shepherd-sheep' | 'rope-knot' | 'connect-master' | 'custom';
    leftItems: CorrespondenceItem[];
    rightItems: CorrespondenceItem[];
    expectedMappings: CorrespondenceMapping[];
    allowPartialMapping: boolean;
    showResult: 'missing' | 'exact' | 'extra' | 'auto-detect';
  };
  correctAnswer: {
    mappings: CorrespondenceMapping[];
    comparison: 'equal' | 'left-more' | 'right-more';
    count: {
      left: number;
      right: number;
      difference: number;
    };
  };
  hints: string[];
  explanation: string;
  relatedConcepts: string[];
  audioInstructions?: string;
  visualEffects: {
    successAnimation: string;
    errorFeedback: string;
    completionCelebration: string;
  };
}

// 对应项
export interface CorrespondenceItem {
  id: string;
  type: 'sheep' | 'stone' | 'knot' | 'apple' | 'box' | 'custom';
  displayName: string;
  imageUrl: string;
  audioUrl?: string;
  position: {
    x: number;
    y: number;
  };
  isConnectable: boolean;
  maxConnections: number;
}

// 对应映射关系
export interface CorrespondenceMapping {
  leftItemId: string;
  rightItemId: string;
  isCorrect: boolean;
  connectionType: 'line' | 'drag-drop' | 'gesture';
}

// 游戏会话数据
export interface CorrespondenceGameSession extends Document {
  id: string;
  studentId: string;
  exerciseId: string;
  gameType: 'correspondence-counting';
  scenario: string;
  startTime: Date;
  endTime?: Date;
  isCompleted: boolean;
  score: number;
  
  // 对应计数特定数据
  userMappings: CorrespondenceMapping[];
  attemptCount: number;
  mistakeDetails: {
    incorrectMappings: CorrespondenceMapping[];
    missedMappings: CorrespondenceMapping[];
    extraMappings: CorrespondenceMapping[];
  };
  
  // 学习表现数据
  timePerMapping: number[];
  hintUsed: boolean;
  finalResult: {
    accuracy: number;
    completionTime: number;
    understanding: 'excellent' | 'good' | 'needs-practice';
  };
}

// Mongoose 模式定义
const CorrespondenceItemSchema = new Schema({
  id: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['sheep', 'stone', 'knot', 'apple', 'box', 'custom'],
    required: true 
  },
  displayName: { type: String, required: true },
  imageUrl: { type: String, required: true },
  audioUrl: { type: String },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true }
  },
  isConnectable: { type: Boolean, default: true },
  maxConnections: { type: Number, default: 1 }
});

const CorrespondenceMappingSchema = new Schema({
  leftItemId: { type: String, required: true },
  rightItemId: { type: String, required: true },
  isCorrect: { type: Boolean, required: true },
  connectionType: { 
    type: String, 
    enum: ['line', 'drag-drop', 'gesture'],
    default: 'line'
  }
});

const CorrespondenceExerciseSchema = new Schema({
  id: { type: String, required: true, unique: true },
  chapterId: { type: Number, required: true },
  type: { type: String, enum: ['correspondence'], required: true },
  subType: { 
    type: String, 
    enum: ['one-to-one-mapping', 'counting-comparison', 'visual-correspondence'],
    required: true 
  },
  difficulty: { type: Number, min: 1, max: 5, required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  
  content: {
    scenario: { 
      type: String, 
      enum: ['shepherd-sheep', 'rope-knot', 'connect-master', 'custom'],
      required: true 
    },
    leftItems: [CorrespondenceItemSchema],
    rightItems: [CorrespondenceItemSchema],
    expectedMappings: [CorrespondenceMappingSchema],
    allowPartialMapping: { type: Boolean, default: false },
    showResult: { 
      type: String, 
      enum: ['missing', 'exact', 'extra', 'auto-detect'],
      default: 'auto-detect'
    }
  },
  
  correctAnswer: {
    mappings: [CorrespondenceMappingSchema],
    comparison: { 
      type: String, 
      enum: ['equal', 'left-more', 'right-more'],
      required: true 
    },
    count: {
      left: { type: Number, required: true },
      right: { type: Number, required: true },
      difference: { type: Number, required: true }
    }
  },
  
  hints: [{ type: String }],
  explanation: { type: String, required: true },
  relatedConcepts: [{ type: String }],
  audioInstructions: { type: String },
  
  visualEffects: {
    successAnimation: { type: String, default: 'sparkle' },
    errorFeedback: { type: String, default: 'shake' },
    completionCelebration: { type: String, default: 'confetti' }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 游戏会话模式
const CorrespondenceGameSessionSchema = new Schema({
  id: { type: String, required: true, unique: true },
  studentId: { type: String, required: true },
  exerciseId: { type: String, required: true },
  gameType: { type: String, enum: ['correspondence-counting'], required: true },
  scenario: { type: String, required: true },
  startTime: { type: Date, default: Date.now },
  endTime: { type: Date },
  isCompleted: { type: Boolean, default: false },
  score: { type: Number, default: 0 },
  
  userMappings: [CorrespondenceMappingSchema],
  attemptCount: { type: Number, default: 0 },
  mistakeDetails: {
    incorrectMappings: [CorrespondenceMappingSchema],
    missedMappings: [CorrespondenceMappingSchema],
    extraMappings: [CorrespondenceMappingSchema]
  },
  
  timePerMapping: [{ type: Number }],
  hintUsed: { type: Boolean, default: false },
  finalResult: {
    accuracy: { type: Number },
    completionTime: { type: Number },
    understanding: { 
      type: String, 
      enum: ['excellent', 'good', 'needs-practice']
    }
  }
}, {
  timestamps: true
});

// 索引
CorrespondenceExerciseSchema.index({ chapterId: 1, difficulty: 1 });
CorrespondenceExerciseSchema.index({ subType: 1 });
CorrespondenceGameSessionSchema.index({ studentId: 1, exerciseId: 1 });
CorrespondenceGameSessionSchema.index({ startTime: 1 });

export const CorrespondenceExercise = mongoose.model<ICorrespondenceExercise>(
  'CorrespondenceExercise', 
  CorrespondenceExerciseSchema
);

export const CorrespondenceGameSession = mongoose.model<CorrespondenceGameSession>(
  'CorrespondenceGameSession', 
  CorrespondenceGameSessionSchema
);