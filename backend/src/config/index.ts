import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';

dotenv.config();

interface DatabaseConfig {
  uri: string;
  options: {
    useNewUrlParser: boolean;
    useUnifiedTopology: boolean;
    maxPoolSize: number;
    serverSelectionTimeoutMS: number;
    socketTimeoutMS: number;
    family: number;
  };
}

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  maxRetriesPerRequest: number;
}

interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshSecret: string;
  refreshExpiresIn: string;
}

interface SecurityConfig {
  encryptionKey: string;
  bcryptRounds: number;
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  corsOrigin: string[];
  corsCredentials: boolean;
  helmetEnabled: boolean;
  trustProxy: boolean;
}

interface FileConfig {
  maxFileSize: number;
  uploadPath: string;
  allowedTypes: string[];
}

interface LogConfig {
  level: string;
  filePath: string;
  maxSize: number;
  maxFiles: number;
}

interface Config {
  app: {
    name: string;
    version: string;
    port: number;
    env: string;
    apiVersion: string;
  };
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: JWTConfig;
  security: SecurityConfig;
  files: FileConfig;
  logging: LogConfig;
  monitoring: {
    enabled: boolean;
    metricsPort: number;
  };
  graphql: {
    playground: boolean;
    introspection: boolean;
  };
  email: {
    host?: string;
    port: number;
    user?: string;
    pass?: string;
    from: string;
  };
}

const getPackageVersion = (): string => {
  try {
    const packagePath = join(__dirname, '../../package.json');
    const packageContent = readFileSync(packagePath, 'utf8');
    const packageData = JSON.parse(packageContent) as { version: string };
    return packageData.version;
  } catch {
    return '1.0.0';
  }
};

const parseEnvArray = (envVar: string | undefined, defaultValue: string[] = []): string[] => {
  if (!envVar) return defaultValue;
  return envVar.split(',').map(item => item.trim()).filter(Boolean);
};

const config: Config = {
  app: {
    name: process.env.APP_NAME || 'hxq-math-backend',
    version: getPackageVersion(),
    port: parseInt(process.env.PORT || '3000', 10),
    env: process.env.NODE_ENV || 'development',
    apiVersion: process.env.API_VERSION || 'v1',
  },
  
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/hxq_math_dev',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4,
    },
  },
  
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: 3,
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },
  
  security: {
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-32-character-encryption-key!!',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15分钟
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    corsOrigin: parseEnvArray(process.env.CORS_ORIGIN, ['http://localhost:3000']),
    corsCredentials: process.env.CORS_CREDENTIALS === 'true',
    helmetEnabled: process.env.HELMET_ENABLED !== 'false',
    trustProxy: process.env.TRUST_PROXY === 'true',
  },
  
  files: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880', 10), // 5MB
    uploadPath: process.env.UPLOAD_PATH || './uploads',
    allowedTypes: parseEnvArray(process.env.ALLOWED_FILE_TYPES, ['image/jpeg', 'image/png']),
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/app.log',
    maxSize: parseInt(process.env.LOG_MAX_SIZE || '10485760', 10), // 10MB
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10),
  },
  
  monitoring: {
    enabled: process.env.MONITORING_ENABLED === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
  },
  
  graphql: {
    playground: process.env.GRAPHQL_PLAYGROUND === 'true',
    introspection: process.env.GRAPHQL_INTROSPECTION === 'true',
  },
  
  email: {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
    from: process.env.SMTP_FROM || '<EMAIL>',
  },
};

// 配置验证
const validateConfig = (): void => {
  const requiredFields = [
    'app.name',
    'app.port',
    'database.uri',
    'jwt.secret',
    'security.encryptionKey',
  ];
  
  for (const field of requiredFields) {
    const value = field.split('.').reduce((obj, key) => obj?.[key], config as any);
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
  
  // 生产环境特殊验证
  if (config.app.env === 'production') {
    if (config.jwt.secret === 'your-super-secret-jwt-key') {
      throw new Error('JWT secret must be changed in production');
    }
    if (config.security.encryptionKey === 'your-32-character-encryption-key!!') {
      throw new Error('Encryption key must be changed in production');
    }
  }
};

validateConfig();

export default config;